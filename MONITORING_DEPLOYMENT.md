# Развертывание мониторинга RemnaWave

Это руководство описывает процесс интеграции веб-приложения мониторинга с существующей инфраструктурой RemnaWave.

## 🏗️ Архитектура

Система мониторинга интегрируется с существующей инфраструктурой RemnaWave:

- **Go HTTP сервер** - API для получения статуса нод и веб-интерфейс (интегрирован в бот)
- **WebSocket** - Real-time обновления статуса
- **NGINX** - Существующий reverse proxy RemnaWave с добавленной конфигурацией мониторинга
- **PostgreSQL** - Существующая база данных RemnaWave
- **SSL сертификаты** - Существующие Cloudflare сертификаты
- **Prometheus + Grafana** - Метрики и визуализация (опционально)

## 📋 Требования

- **Установленная система RemnaWave** (панель или панель+нода)
- Ubuntu/Debian сервер с правами root
- Docker и Docker Compose (уже установлены с RemnaWave)
- NGINX (уже установлен и настроен с RemnaWave)
- SSL сертификаты Cloudflare (уже настроены с RemnaWave)
- Домен для мониторинга (например, status.unveilvpn.com)

## 🚀 Быстрое развертывание

### Предварительные требования

**ВАЖНО:** Перед развертыванием мониторинга убедитесь, что:

1. RemnaWave уже установлен и работает
2. У вас есть доступ к серверу с правами root
3. Настроен домен для мониторинга (например, status.unveilvpn.com)
4. SSL сертификат для домена мониторинга получен через RemnaWave скрипт

### 1. Автоматическое развертывание

```bash
# Клонируйте репозиторий в директорию RemnaWave
cd /opt/remnawave
git clone <repository-url> monitoring
cd monitoring

# Запустите скрипт интеграции мониторинга
sudo ./scripts/deploy-monitoring.sh status.unveilvpn.com
```

### 2. Ручное развертывание

#### Шаг 1: Проверка существующей инфраструктуры

```bash
# Проверьте, что RemnaWave установлен
ls -la /opt/remnawave/

# Проверьте статус контейнеров RemnaWave
cd /opt/remnawave && docker-compose ps

# Проверьте конфигурацию NGINX
sudo nginx -t
```

#### Шаг 2: Настройка переменных окружения

```bash
# Отредактируйте .env файл в директории RemnaWave
cd /opt/remnawave
nano .env
```

Добавьте или обновите следующие переменные:

```env
# Основные настройки мониторинга
SERVER_STATUS_URL="https://status.unveilvpn.com"

# Дополнительные настройки мониторинга (опционально)
MONITORING_UPDATE_INTERVAL=30
MONITORING_TIMEOUT=10
MONITORING_MAX_RETRIES=3
MONITORING_ENABLE_WEBSOCKET=true
MONITORING_ENABLE_NOTIFICATIONS=true
```

#### Шаг 3: Настройка SSL сертификатов

```bash
# Получите SSL сертификат для домена мониторинга через RemnaWave скрипт
remnawave_reverse

# Выберите: Управление сертификатами домена -> Сгенерировать новые сертификаты
# Введите домен: status.unveilvpn.com
```

#### Шаг 4: Интеграция с NGINX

```bash
# Создайте резервную копию конфигурации NGINX
cp /opt/remnawave/nginx.conf /opt/remnawave/nginx.conf.backup

# Добавьте конфигурацию мониторинга в существующий файл
# (это будет сделано автоматически скриптом развертывания)
```

#### Шаг 5: Обновление Docker Compose

```bash
# Скопируйте файлы мониторинга в директорию RemnaWave
cp -r monitoring/static /opt/remnawave/
cp monitoring/docker-compose.monitoring.yaml /opt/remnawave/

# Перезапустите контейнеры с мониторингом
cd /opt/remnawave
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yaml up -d
```

## 🔧 Конфигурация

### Переменные окружения

| Переменная | Описание | Обязательная | Значение по умолчанию |
|------------|----------|--------------|----------------------|
| `SERVER_STATUS_URL` | URL веб-интерфейса мониторинга | Да | - |
| `REMNAWAVE_API_URL` | URL API RemnaWave (автоматически из конфигурации) | Нет | http://localhost:3000 |
| `REMNAWAVE_API_KEY` | API ключ RemnaWave (автоматически из конфигурации) | Нет | - |
| `MONITORING_UPDATE_INTERVAL` | Интервал обновления статуса (секунды) | Нет | 30 |
| `MONITORING_TIMEOUT` | Таймаут запросов (секунды) | Нет | 10 |
| `MONITORING_MAX_RETRIES` | Максимальное количество повторов | Нет | 3 |
| `MONITORING_ENABLE_WEBSOCKET` | Включить WebSocket обновления | Нет | true |
| `MONITORING_ENABLE_NOTIFICATIONS` | Включить уведомления | Нет | true |

### NGINX конфигурация

Конфигурация мониторинга интегрируется с существующим NGINX RemnaWave:

- **Использование существующих SSL сертификатов** Cloudflare
- **Интеграция с существующим reverse proxy**
- **Поддержка wildcard сертификатов**
- **Совместимость с security headers RemnaWave**
- **Использование существующих rate limiting правил**

### Docker Compose профили

- **default** - Базовое развертывание (бот + база данных)
- **monitoring-full** - Полный стек мониторинга (+ Prometheus + Grafana)

## 📊 Доступ к интерфейсам

После успешного развертывания доступны следующие интерфейсы:

- **Веб-мониторинг**: https://status.unveilvpn.com
- **API**: https://status.unveilvpn.com/api/v1/monitoring/
- **WebSocket**: wss://status.unveilvpn.com/ws/monitoring
- **Health Check**: https://status.unveilvpn.com/healthcheck
- **Grafana** (если включен): http://localhost:3000 (admin/admin123)
- **Prometheus** (если включен): http://localhost:9090

## 🔍 API Эндпоинты

### Основные эндпоинты

```bash
# Получить статус всех нод
GET /api/v1/monitoring/nodes

# Получить статус конкретной ноды
GET /api/v1/monitoring/nodes/{id}

# Получить системный статус
GET /api/v1/monitoring/system

# Health check
GET /api/v1/monitoring/health
```

### Примеры запросов

```bash
# Получить статус всех нод
curl https://status.unveilvpn.com/api/v1/monitoring/nodes

# Получить системный статус
curl https://status.unveilvpn.com/api/v1/monitoring/system

# Health check
curl https://status.unveilvpn.com/api/v1/monitoring/health
```

## 🛠️ Управление сервисом

### Docker Compose команды

```bash
# Просмотр статуса
docker-compose ps

# Просмотр логов
docker-compose logs -f

# Перезапуск сервисов
docker-compose restart

# Остановка сервисов
docker-compose down

# Обновление образов
docker-compose pull && docker-compose up -d
```

### Systemd сервис

```bash
# Статус сервиса
sudo systemctl status remnawave-monitoring

# Перезапуск сервиса
sudo systemctl restart remnawave-monitoring

# Просмотр логов
sudo journalctl -u remnawave-monitoring -f
```

## 🔒 Безопасность

### SSL/TLS

- Автоматическое получение и обновление сертификатов через Let's Encrypt
- Поддержка TLS 1.2 и 1.3
- HSTS headers
- Secure ciphers

### Security Headers

- X-Frame-Options: SAMEORIGIN
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Content-Security-Policy
- Referrer-Policy

### Rate Limiting

- API: 10 запросов/сек с burst 20
- Web: 5 запросов/сек с burst 10

## 📈 Мониторинг и логирование

### Логи

- **NGINX**: `/var/log/nginx/status.unveilvpn.com.*.log`
- **Docker**: `docker-compose logs`
- **Systemd**: `journalctl -u remnawave-monitoring`

### Метрики (при включенном Prometheus)

- Системные метрики (CPU, память, диск)
- Метрики приложения
- Метрики NGINX
- Метрики PostgreSQL

## 🚨 Устранение неполадок

### Проверка статуса сервисов

```bash
# Проверка контейнеров RemnaWave
cd /opt/remnawave && docker-compose ps

# Проверка NGINX
sudo nginx -t
sudo systemctl status nginx

# Проверка SSL сертификатов Cloudflare
ls -la /etc/nginx/ssl/

# Проверка портов
sudo netstat -tlnp | grep :8080
sudo netstat -tlnp | grep :443

# Проверка логов RemnaWave
cd /opt/remnawave && docker-compose logs remnawave
```

### Частые проблемы

1. **SSL сертификат не найден**
   ```bash
   # Получите сертификат через RemnaWave скрипт
   remnawave_reverse
   # Выберите: Управление сертификатами домена -> Сгенерировать новые сертификаты
   ```

2. **API мониторинга недоступен**
   ```bash
   # Проверьте health check
   curl http://localhost:8080/healthcheck

   # Проверьте логи контейнера
   cd /opt/remnawave && docker-compose logs remnawave

   # Проверьте переменные окружения
   cd /opt/remnawave && grep MONITORING .env
   ```

3. **WebSocket не работает**
   ```bash
   # Проверьте NGINX конфигурацию
   sudo nginx -t

   # Проверьте, что upstream добавлен
   grep -A5 "upstream monitoring_backend" /opt/remnawave/nginx.conf

   # Перезагрузите NGINX
   sudo systemctl reload nginx
   ```

4. **Мониторинг не отображается в боте**
   ```bash
   # Проверьте переменную SERVER_STATUS_URL
   cd /opt/remnawave && grep SERVER_STATUS_URL .env

   # Перезапустите контейнеры
   docker-compose restart
   ```

## 📞 Поддержка

При возникновении проблем:

1. Проверьте логи: `docker-compose logs -f`
2. Убедитесь, что все сервисы запущены: `docker-compose ps`
3. Проверьте конфигурацию NGINX: `sudo nginx -t`
4. Проверьте SSL сертификаты: `sudo certbot certificates`

## 🔄 Обновление

```bash
# Получите последние изменения
git pull

# Пересоберите и перезапустите контейнеры
docker-compose build --no-cache
docker-compose up -d

# Проверьте статус
docker-compose ps
```

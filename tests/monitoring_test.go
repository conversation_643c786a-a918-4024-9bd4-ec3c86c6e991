package tests

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"remnawave-tg-shop-bot/internal/monitoring"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockRemnaWaveClient для тестирования
type MockRemnaWaveClient struct {
	shouldFail bool
}

func (m *MockRemnaWaveClient) GetNodes(ctx context.Context) (*[]interface{}, error) {
	if m.shouldFail {
		return nil, assert.AnError
	}

	// Возвращаем тестовые данные
	mockNodes := []interface{}{
		map[string]interface{}{
			"UUID":             "550e8400-e29b-41d4-a716-************",
			"Name":             "Test-Node-1",
			"Address":          "test1.example.com",
			"Port":             map[string]interface{}{"Value": 443, "Null": false},
			"IsConnected":      true,
			"IsDisabled":       false,
			"IsConnecting":     false,
			"IsNodeOnline":     true,
			"IsXrayRunning":    true,
			"LastStatusChange": map[string]interface{}{"Value": time.Now(), "Null": false},
			"NodeVersion":      map[string]interface{}{"Value": "1.8.4", "Null": false},
			"XrayUptime":       "1d 2h 30m",
			"TrafficUsedBytes": map[string]interface{}{"Value": 1024 * 1024 * 1024, "Null": false},
			"UsersOnline":      map[string]interface{}{"Value": 5, "Null": false},
			"CountryCode":      "US",
		},
	}

	return &mockNodes, nil
}

func (m *MockRemnaWaveClient) Ping(ctx context.Context) error {
	if m.shouldFail {
		return assert.AnError
	}
	return nil
}

func TestMonitoringService_GetAllNodes(t *testing.T) {
	// Тест получения всех нод
	t.Run("successful get all nodes", func(t *testing.T) {
		// Пока что просто проверяем, что тест компилируется
		// В будущем здесь будет полноценный тест с mock клиентом
		assert.True(t, true)
	})
}

func TestMonitoringHandler_GetAllNodes(t *testing.T) {

	t.Run("GET /api/v1/monitoring/nodes", func(t *testing.T) {
		// Создаем тестовый HTTP запрос
		req := httptest.NewRequest("GET", "/api/v1/monitoring/nodes", nil)
		w := httptest.NewRecorder()

		// Создаем простой обработчик для тестирования
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := monitoring.MonitoringResponse{
				Success: true,
				Data: []monitoring.NodeStatus{
					{
						ID:       "550e8400-e29b-41d4-a716-************",
						Name:     "Test-Node-1",
						Address:  "test1.example.com",
						Status:   "online",
						LastSeen: time.Now(),
						Latency:  45,
						Traffic: monitoring.Traffic{
							Upload:   1024 * 1024,
							Download: 2 * 1024 * 1024,
							Total:    3 * 1024 * 1024,
						},
						Uptime:   "1d 2h 30m",
						Version:  "1.8.4",
						Location: "United States",
						Load: monitoring.Load{
							CPU:    25.5,
							Memory: 60.2,
							Users:  5,
						},
					},
				},
				Meta: monitoring.Meta{
					Timestamp: time.Now(),
					Version:   "1.0",
				},
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
		})

		// Выполняем запрос
		handler.ServeHTTP(w, req)

		// Проверяем результат
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		// Парсим ответ
		var response monitoring.MonitoringResponse
		err := json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)

		assert.True(t, response.Success)
		assert.NotEmpty(t, response.Data)
	})
}

func TestMonitoringHandler_GetSystemStatus(t *testing.T) {
	t.Run("GET /api/v1/monitoring/system", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/monitoring/system", nil)
		w := httptest.NewRecorder()

		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := monitoring.MonitoringResponse{
				Success: true,
				Data: monitoring.SystemStatus{
					TotalNodes:     3,
					OnlineNodes:    2,
					OfflineNodes:   1,
					ErrorNodes:     0,
					LastUpdate:     time.Now(),
					SystemHealth:   "degraded",
					AverageLatency: 45,
					TotalTraffic: monitoring.Traffic{
						Upload:   10 * 1024 * 1024,
						Download: 20 * 1024 * 1024,
						Total:    30 * 1024 * 1024,
					},
				},
				Meta: monitoring.Meta{
					Timestamp: time.Now(),
					Version:   "1.0",
				},
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
		})

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response monitoring.MonitoringResponse
		err := json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)

		assert.True(t, response.Success)

		// Проверяем структуру SystemStatus
		systemStatus, ok := response.Data.(map[string]interface{})
		require.True(t, ok)

		assert.Equal(t, float64(3), systemStatus["total_nodes"])
		assert.Equal(t, float64(2), systemStatus["online_nodes"])
		assert.Equal(t, "degraded", systemStatus["system_health"])
	})
}

func TestMonitoringHandler_HealthCheck(t *testing.T) {
	t.Run("GET /api/v1/monitoring/health", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/monitoring/health", nil)
		w := httptest.NewRecorder()

		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := monitoring.MonitoringResponse{
				Success: true,
				Data: []monitoring.HealthCheckResult{
					{
						Service:   "remnawave_api",
						Status:    "ok",
						Message:   "API is responding",
						Timestamp: time.Now(),
						Duration:  150,
					},
					{
						Service:   "nodes_status",
						Status:    "ok",
						Message:   "2/3 nodes online",
						Timestamp: time.Now(),
						Duration:  0,
					},
				},
				Meta: monitoring.Meta{
					Timestamp: time.Now(),
					Version:   "1.0",
				},
			}

			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
		})

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response monitoring.MonitoringResponse
		err := json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)

		assert.True(t, response.Success)
	})
}

func TestWebSocketConnection(t *testing.T) {
	t.Run("WebSocket connection and message handling", func(t *testing.T) {
		// Создаем тестовый WebSocket сервер
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			upgrader := websocket.Upgrader{
				CheckOrigin: func(r *http.Request) bool {
					return true
				},
			}

			conn, err := upgrader.Upgrade(w, r, nil)
			if err != nil {
				t.Errorf("Failed to upgrade connection: %v", err)
				return
			}
			defer conn.Close()

			// Отправляем тестовое сообщение
			message := monitoring.WebSocketMessage{
				Type: "system_update",
				Data: monitoring.SystemStatus{
					TotalNodes:   3,
					OnlineNodes:  2,
					SystemHealth: "healthy",
				},
				Meta: monitoring.Meta{
					Timestamp: time.Now(),
					Version:   "1.0",
				},
			}

			err = conn.WriteJSON(message)
			if err != nil {
				t.Errorf("Failed to write JSON: %v", err)
			}
		}))
		defer server.Close()

		// Подключаемся к WebSocket серверу
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http")
		conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Читаем сообщение
		var message monitoring.WebSocketMessage
		err = conn.ReadJSON(&message)
		require.NoError(t, err)

		assert.Equal(t, "system_update", message.Type)
		assert.NotNil(t, message.Data)
		assert.Equal(t, "1.0", message.Meta.Version)
	})
}

func TestCORSHeaders(t *testing.T) {
	t.Run("CORS headers are set correctly", func(t *testing.T) {
		req := httptest.NewRequest("OPTIONS", "/api/v1/monitoring/nodes", nil)
		w := httptest.NewRecorder()

		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Устанавливаем CORS заголовки
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}
		})

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Equal(t, "GET, OPTIONS", w.Header().Get("Access-Control-Allow-Methods"))
		assert.Equal(t, "Content-Type", w.Header().Get("Access-Control-Allow-Headers"))
	})
}

func TestErrorHandling(t *testing.T) {
	t.Run("API error handling", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/api/v1/monitoring/nodes", nil)
		w := httptest.NewRecorder()

		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := monitoring.MonitoringResponse{
				Success: false,
				Error:   "Failed to fetch nodes",
				Meta: monitoring.Meta{
					Timestamp: time.Now(),
					Version:   "1.0",
				},
			}

			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusInternalServerError)
			json.NewEncoder(w).Encode(response)
		})

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response monitoring.MonitoringResponse
		err := json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)

		assert.False(t, response.Success)
		assert.Equal(t, "Failed to fetch nodes", response.Error)
	})
}

// Benchmark тесты
func BenchmarkMonitoringAPI(b *testing.B) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := monitoring.MonitoringResponse{
			Success: true,
			Data: []monitoring.NodeStatus{
				{
					ID:       "test-node-1",
					Name:     "Test Node 1",
					Status:   "online",
					LastSeen: time.Now(),
				},
			},
			Meta: monitoring.Meta{
				Timestamp: time.Now(),
				Version:   "1.0",
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("GET", "/api/v1/monitoring/nodes", nil)
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
	}
}

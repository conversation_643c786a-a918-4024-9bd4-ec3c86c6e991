TELEGRAM_TOKEN=1234567890
ADMIN_TELEGRAM_ID=1234567890

DATABASE_URL=************************************/postgres?sslmode=disable
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=postgres

REMNAWAVE_URL=https://example.com
REMNAWAVE_MODE=remote
REMNAWAVE_TOKEN=token
REMNAWAVE_SECRET_KEY_NAME=NAME
REMNAWAVE_SECRET_KEY_VALUE=VALUE
REMNAWAVE_TAG=TELEGRAM_BOT
SQUAD_UUIDS=
MINI_APP_URL=


CRYPTO_PAY_ENABLED=true
CRYPTO_PAY_TOKEN=token
CRYPTO_PAY_URL=https://pay.crypt.bot

YOOKASA_ENABLED=true
YOOKASA_SECRET_KEY=key
YOOKASA_SHOP_ID=id
YOOKASA_URL=https://api.yookassa.ru/v3
YOOKASA_EMAIL=<EMAIL>

TELEGRAM_STARS_ENABLED=true

TRIBUTE_ENABLED=true
TRIBUTE_WEBHOOK_URL=/webhook/tribute
TRIBUTE_API_KEY=your_tribute_api_key
TRIBUTE_PAYMENT_URL=https://tribute.example.com/payment

CRYPTOMUS_ENABLED=true
CRYPTOMUS_MERCHANT_ID=your_merchant_id
CRYPTOMUS_API_KEY=your_api_key
CRYPTOMUS_WEBHOOK_URL=/webhook/cryptomus

TRIAL_TRAFFIC_LIMIT=20
TRIAL_DAYS=2
REFERRAL_DAYS=7

SERVER_STATUS_URL="https://status.unveilvpn.com"
SUPPORT_URL="https://example.com/support"
FEEDBACK_URL="https://example.com/feedback"
CHANNEL_URL="https://t.me/examplechannel"

# Monitoring Configuration
MONITORING_UPDATE_INTERVAL=30
MONITORING_TIMEOUT=10
MONITORING_MAX_RETRIES=3
MONITORING_ENABLE_WEBSOCKET=true
MONITORING_ENABLE_NOTIFICATIONS=true

# GitHub Access Token for private repositories
# Create token at: https://github.com/settings/tokens
# Required scopes: repo (for private repositories)
GITHUB_TOKEN=your_github_personal_access_token_here
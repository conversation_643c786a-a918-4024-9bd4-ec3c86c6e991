# Платежные системы

Этот документ описывает интеграцию платежных систем в Telegram бот для VPN подписок.

## Поддерживаемые платежные системы

### 1. Telegram Payments
- **Описание**: Встроенная система платежей Telegram
- **Статус**: Активна
- **Валюты**: Зависит от провайдера
- **Особенности**: Интегрирована в интерфейс Telegram

### 2. YooKasa (ЮKassa)
- **Описание**: Российская платежная система от Яндекса
- **Статус**: Активна
- **Валюты**: RUB, USD, EUR и другие
- **Особенности**: Поддержка банковских карт, электронных кошельков

### 3. CryptoPay
- **Описание**: Криптовалютная платежная система
- **Статус**: Активна
- **Валюты**: BTC, ETH, USDT, TON и другие криптовалюты
- **Особенности**: Мгновенные переводы, низкие комиссии

### 4. Tribute
- **Описание**: Внешняя платежная система
- **Статус**: Активна
- **Валюты**: Зависит от конфигурации
- **Особенности**: Внешний URL для оплаты, webhook поддержка

### 5. Cryptomus (НОВОЕ)
- **Описание**: Криптовалютная платежная система Cryptomus API Business
- **Статус**: Активна
- **Валюты**: BTC, ETH, USDT, LTC и другие криптовалюты
- **Особенности**: 
  - Автоматическое создание инвойсов
  - Webhook уведомления о платежах
  - Поддержка множества блокчейн сетей
  - MD5 подпись для безопасности

## Конфигурация

### Переменные окружения

```bash
# Telegram Payments
TELEGRAM_PAYMENTS_ENABLED=true
TELEGRAM_PAYMENTS_TOKEN=your_payments_token

# YooKasa
YOOKASA_ENABLED=true
YOOKASA_SHOP_ID=your_shop_id
YOOKASA_SECRET_KEY=your_secret_key

# CryptoPay
CRYPTOPAY_ENABLED=true
CRYPTOPAY_TOKEN=your_cryptopay_token

# Tribute
TRIBUTE_ENABLED=true
TRIBUTE_WEBHOOK_URL=/webhook/tribute
TRIBUTE_API_KEY=your_tribute_api_key
TRIBUTE_PAYMENT_URL=https://tribute.example.com/payment

# Cryptomus
CRYPTOMUS_ENABLED=true
CRYPTOMUS_MERCHANT_ID=your_merchant_id
CRYPTOMUS_API_KEY=your_api_key
CRYPTOMUS_WEBHOOK_URL=/webhook/cryptomus
```

### Настройка Cryptomus

1. **Регистрация**: Зарегистрируйтесь на [cryptomus.com](https://cryptomus.com)
2. **Получение API ключей**: 
   - Merchant ID - идентификатор мерчанта
   - API Key - секретный ключ для подписи запросов
3. **Настройка webhook**: Укажите URL вашего сервера + `/webhook/cryptomus`
4. **Тестирование**: Используйте тестовую среду для проверки интеграции

## Архитектура

### Структура кода

```
internal/
├── payment/
│   └── payment.go          # Основной сервис платежей
├── cryptomus/
│   ├── client.go           # HTTP клиент для Cryptomus API
│   ├── models.go           # Структуры данных
│   ├── webhook.go          # Обработчик webhook'ов
│   └── client_test.go      # Тесты
├── tribute/
│   └── tribute.go          # Обработчик Tribute
└── database/
    └── purchase.go         # Модели покупок
```

### Процесс платежа

1. **Создание покупки**: Пользователь выбирает тариф и способ оплаты
2. **Создание инвойса**: Система создает инвойс в выбранной платежной системе
3. **Перенаправление**: Пользователь перенаправляется на страницу оплаты
4. **Обработка webhook**: После оплаты система получает уведомление
5. **Активация подписки**: При успешной оплате активируется VPN подписка

### Безопасность

- **Подписи**: Все webhook'и проверяются на подлинность
- **HTTPS**: Обязательное использование HTTPS для webhook'ов
- **Валидация**: Проверка всех входящих данных
- **Логирование**: Детальное логирование всех операций

## API Cryptomus

### Создание инвойса

```http
POST https://api.cryptomus.com/v1/payment
Content-Type: application/json
merchant: YOUR_MERCHANT_ID
sign: MD5_SIGNATURE

{
  "amount": "100.00",
  "currency": "USD",
  "order_id": "purchase_123",
  "url_return": "https://yoursite.com/return",
  "url_callback": "https://yoursite.com/webhook/cryptomus"
}
```

### Webhook формат

```json
{
  "type": "payment",
  "uuid": "payment-uuid",
  "order_id": "purchase_123",
  "amount": "100.00",
  "payment_amount": "100.00",
  "status": "paid",
  "currency": "USD",
  "network": "BITCOIN",
  "txid": "transaction-hash",
  "sign": "webhook-signature"
}
```

## Тестирование

Запуск тестов:

```bash
# Тесты Cryptomus
go test ./internal/cryptomus -v

# Все тесты
go test ./... -v
```

## Мониторинг

### Логи

Система логирует:
- Создание инвойсов
- Получение webhook'ов
- Ошибки обработки платежей
- Изменения статусов покупок

### Метрики

Рекомендуется отслеживать:
- Количество успешных платежей
- Время обработки webhook'ов
- Ошибки платежных систем
- Конверсию по способам оплаты

## Устранение неполадок

### Частые проблемы

1. **Webhook не приходят**:
   - Проверьте URL webhook'а
   - Убедитесь, что сервер доступен извне
   - Проверьте логи на ошибки

2. **Неверная подпись**:
   - Проверьте правильность API ключей
   - Убедитесь в корректности алгоритма подписи

3. **Платеж не обрабатывается**:
   - Проверьте статус в платежной системе
   - Проверьте логи обработки webhook'ов
   - Убедитесь в корректности order_id

### Отладка

Включите детальное логирование:

```bash
export LOG_LEVEL=debug
```

Проверьте статус платежа вручную через API платежной системы.

# Документация по миграции на React

## 🎯 Обзор

Страница статусов серверов UnveilVPN была полностью переписана с HTML+CSS на современный стек **React + Tailwind CSS**. Миграция завершена успешно с сохранением всей функциональности и улучшением архитектуры.

## 🏗️ Архитектура React приложения

### Технологический стек
- **React 18.3.1** - UI библиотека
- **TypeScript** - типобезопасность
- **Tailwind CSS 3.4.17** - стилизация
- **Vite** - сборщик и dev сервер

### Структура компонентов

```
src/
├── components/
│   ├── layout/              # Layout компоненты
│   │   ├── Header.tsx       # Шапка с переключателями
│   │   ├── Footer.tsx       # Подвал
│   │   └── Layout.tsx       # Основной layout
│   ├── dashboard/           # Dashboard компоненты  
│   │   ├── SystemMetrics.tsx    # 4 карточки метрик
│   │   ├── SystemHealth.tsx     # Статус здоровья системы
│   │   ├── ServerFilters.tsx    # Фильтры и поиск
│   │   ├── ServerCard.tsx       # Карточка сервера
│   │   └── ServerGrid.tsx       # Сетка серверов
│   └── ui/                  # UI компоненты
│       ├── Button.tsx       # Кнопки
│       ├── Badge.tsx        # Бейджи статусов
│       ├── SearchInput.tsx  # Поле поиска
│       └── LoadingSpinner.tsx # Индикатор загрузки
├── hooks/                   # React хуки
│   ├── useTranslation.ts    # Переводы
│   ├── useServerData.ts     # API данные
│   └── useInterval.ts       # Интервалы
├── contexts/                # React Context
│   └── AppContext.tsx       # Глобальное состояние
├── types/                   # TypeScript типы
│   └── index.ts             # Интерфейсы API
├── utils/                   # Утилиты
│   ├── index.ts             # Общие функции
│   └── translations.ts      # Переводы
└── App.tsx                  # Главный компонент
```

## 🔧 Ключевые особенности

### Монолитный дизайн
- **Фиксированная ширина**: `max-w-7xl mx-auto`
- **Консистентные иконки**: `w-5 h-5`, `w-6 h-6`, `w-8 h-8`
- **Стандартные даты**: `DD.MM.YYYY, HH:mm:ss`
- **Без сдвигов**: `min-height` для всех элементов

### Адаптивность
- **Мобильные** (320px-767px): 2 колонки метрик, 1 колонка серверов
- **Планшеты** (768px-1279px): 4 колонки метрик, 2 колонки серверов  
- **Десктопы** (1280px+): 4 колонки метрик, 3 колонки серверов

### Функциональность
- ✅ Переключение темы (светлая/темная)
- ✅ Переключение языка (EN/RU)
- ✅ Фильтрация серверов
- ✅ Поиск по названию/местоположению
- ✅ Автообновление каждые 30 секунд
- ✅ Скрытие IP-адресов для безопасности

## 🚀 Процесс сборки и развертывания

### Разработка (если нужны изменения)

1. **Создание нового React проекта:**
```bash
npm create vite@latest status-dashboard -- --template react-ts
cd status-dashboard
npm install
```

2. **Установка Tailwind CSS:**
```bash
npm install -D tailwindcss postcss autoprefixer @types/node
```

3. **Разработка:**
```bash
npm run dev  # Запуск dev сервера
```

### Сборка для продакшена

1. **Сборка приложения:**
```bash
cd status-dashboard
npm run build
```

2. **Развертывание:**
```bash
# Копирование собранных файлов
cp -r dist/* ../static/
```

### Структура собранных файлов
```
static/
├── index.html                    # React SPA
├── assets/
│   ├── index-[hash].js          # Скомпилированный React
│   ├── index-[hash].css         # Скомпилированный Tailwind
│   └── logo.svg                 # Логотип
└── status/assets/logo.svg       # Копия для правильных путей
```

## 🔌 API интеграция

### Эндпоинты
- `GET /api/v1/monitoring/system` - системные метрики
- `GET /api/v1/monitoring/nodes/status` - статусы серверов

### Типы данных
```typescript
interface SystemStatus {
  total_nodes: number;
  online_nodes: number;
  offline_nodes: number;
  error_nodes: number;
  last_update: string;
  system_health: 'healthy' | 'degraded' | 'down';
  average_latency_ms: number;
}

interface NodeStatus {
  id: string;
  name: string;
  address: string;  // IP скрыт для безопасности
  status: 'online' | 'offline' | 'error' | 'connecting' | 'disabled';
  last_seen: string;
  latency_ms: number;
  location?: string;
  version?: string;
  uptime_percentage?: number;
  load: {
    cpu_percent: number;
    memory_percent: number;
    active_users: number;
  };
}
```

## 🔒 Безопасность

### Скрытие IP-адресов
```typescript
function hideIPAddress(ip: string): string {
  const parts = ip.split('.');
  if (parts.length === 4) {
    return `${parts[0]}.xxx.xxx.xxx`;
  }
  return ip.substring(0, 3) + 'x'.repeat(Math.max(8, ip.length - 3));
}
```

### Валидация данных
- TypeScript интерфейсы для всех API ответов
- Проверка структуры данных перед отображением
- Graceful fallbacks при ошибках

## 🎨 Стилизация

### Tailwind CSS конфигурация
- **Темная тема**: `dark:` префиксы
- **Кастомные цвета**: primary палитра
- **Анимации**: fade-in-up для плавности
- **Шрифты**: Inter font family

### Кастомные классы
```css
.badge-success { @apply bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300; }
.badge-error { @apply bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300; }
.badge-warning { @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300; }
```

## 🧪 Тестирование

### Проверка работоспособности
1. Запустить HTTP сервер: `cd static && python3 -m http.server 8080`
2. Открыть `http://localhost:8080`
3. Проверить:
   - Загрузку всех ресурсов
   - API запросы к правильным эндпоинтам
   - Переключение темы/языка
   - Адаптивность на разных экранах

### Интеграция с бэкендом
- React приложение автоматически интегрируется с существующим Go сервером
- Маршруты `/status` и `/status/` обслуживают React SPA
- API эндпоинты остаются без изменений

## ✅ Результат миграции

**Преимущества новой архитектуры:**
- 🚀 Современный технологический стек
- 📱 Полная адаптивность
- 🎨 Улучшенный UI/UX
- 🔧 Легкая поддержка и расширение
- 📦 Компонентная архитектура
- 🔒 Типобезопасность с TypeScript
- ⚡ Высокая производительность

**Сохранена вся функциональность:**
- ✅ Все API интеграции
- ✅ Безопасность (скрытие IP)
- ✅ Автообновление данных
- ✅ Переключение темы/языка
- ✅ Фильтрация и поиск
- ✅ Монолитный дизайн

Миграция завершена успешно! 🎉

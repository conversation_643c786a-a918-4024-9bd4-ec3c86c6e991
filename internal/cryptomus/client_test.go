package cryptomus

import (
	"encoding/json"
	"testing"
)

func TestGenerateSign(t *testing.T) {
	client := &Client{
		merchantID: "test_merchant",
		apiKey:     "test_api_key",
	}

	// Тестовые данные
	data := map[string]interface{}{
		"amount":   "100.00",
		"currency": "USD",
		"order_id": "test_order_123",
	}

	// Сериализуем данные в JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		t.Fatalf("Ошибка сериализации данных: %v", err)
	}

	// Генерируем подпись
	signature := client.generateSign(jsonData)

	// Проверяем, что подпись не пустая
	if signature == "" {
		t.Error("Подпись не должна быть пустой")
	}

	// Проверяем, что подпись имеет правильную длину (MD5 = 32 символа)
	if len(signature) != 32 {
		t.<PERSON><PERSON><PERSON>("Ожидалась длина подписи 32 символа, получено: %d", len(signature))
	}

	// Проверяем, что одинаковые данные дают одинаковую подпись
	signature2 := client.generateSign(jsonData)
	if signature != signature2 {
		t.Error("Одинаковые данные должны давать одинаковую подпись")
	}
}

func TestNewCryptomusClient(t *testing.T) {
	merchantID := "test_merchant"
	apiKey := "test_api_key"

	client := NewCryptomusClient(merchantID, apiKey)

	if client == nil {
		t.Error("Клиент не должен быть nil")
	}

	if client.merchantID != merchantID {
		t.Errorf("Ожидался merchantID: %s, получен: %s", merchantID, client.merchantID)
	}

	if client.apiKey != apiKey {
		t.Errorf("Ожидался apiKey: %s, получен: %s", apiKey, client.apiKey)
	}

	if client.baseURL != "https://api.cryptomus.com/v1" {
		t.Errorf("Ожидался baseURL: https://api.cryptomus.com/v1, получен: %s", client.baseURL)
	}
}

func TestCreateInvoiceRequest(t *testing.T) {
	req := &InvoiceRequest{
		Amount:     "100.00",
		Currency:   "USD",
		OrderID:    "test_order_123",
		URLReturn:  "https://example.com/return",
		URLSuccess: "https://example.com/success",
	}

	// Проверяем, что запрос создается без ошибок
	// Здесь мы не можем протестировать реальный HTTP запрос без мокирования
	// но можем проверить валидацию данных
	if req.Amount == "" {
		t.Error("Amount не должен быть пустым")
	}

	if req.Currency == "" {
		t.Error("Currency не должен быть пустым")
	}

	if req.OrderID == "" {
		t.Error("OrderID не должен быть пустым")
	}
}

func TestWebhookDataValidation(t *testing.T) {
	// Тестируем валидацию данных webhook'а
	txID := "test-tx-id"
	fromAddress := "test-from-address"

	webhookData := &WebhookData{
		UUID:             "test-uuid-123",
		OrderID:          "purchase_123",
		Amount:           "100.00",
		PaymentAmount:    "100.00",
		PaymentAmountUSD: "100.00",
		MerchantAmount:   "95.00",
		Commission:       "5.00",
		Status:           PaymentStatusPaid,
		Currency:         "USD",
		PayerCurrency:    "USD",
		Network:          "BITCOIN",
		TxID:             &txID,
		From:             &fromAddress,
	}

	// Проверяем основные поля
	if webhookData.UUID == "" {
		t.Error("UUID не должен быть пустым")
	}

	if webhookData.OrderID == "" {
		t.Error("OrderID не должен быть пустым")
	}

	if webhookData.Status == "" {
		t.Error("Status не должен быть пустым")
	}

	// Проверяем валидные статусы
	validStatuses := []string{
		PaymentStatusPaid,
		PaymentStatusPaidOver,
		PaymentStatusFail,
		PaymentStatusCancel,
		PaymentStatusSystemFail,
		PaymentStatusRefundPaid,
	}

	isValidStatus := false
	for _, status := range validStatuses {
		if webhookData.Status == status {
			isValidStatus = true
			break
		}
	}

	if !isValidStatus {
		t.Errorf("Неизвестный статус: %s", webhookData.Status)
	}
}

package cryptomus

import (
	"context"
	"io"
	"log/slog"
	"net/http"
	"remnawave-tg-shop-bot/internal/database"
	"strconv"
	"strings"
	"time"
)

// PaymentProcessor интерфейс для обработки платежей
type PaymentProcessor interface {
	ProcessPurchaseById(ctx context.Context, purchaseID int64) error
}

// WebhookHandler обрабатывает webhook'и от Cryptomus
type WebhookHandler struct {
	client             *Client
	paymentProcessor   PaymentProcessor
	customerRepository *database.CustomerRepository
}

// NewWebhookHandler создает новый обработчик webhook'ов
func NewWebhookHandler(
	client *Client,
	paymentProcessor PaymentProcessor,
	customerRepository *database.CustomerRepository,
) *WebhookHandler {
	return &WebhookHandler{
		client:             client,
		paymentProcessor:   paymentProcessor,
		customerRepository: customerRepository,
	}
}

// WebHookHandler возвращает HTTP обработчик для webhook'ов
func (h *WebhookHandler) WebHookHandler() http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx, cancel := context.WithTimeout(r.Context(), time.Second*60)
		defer cancel()

		body, err := io.ReadAll(r.Body)
		if err != nil {
			slog.Error("webhook: read body error", "error", err)
			http.Error(w, "invalid body", http.StatusBadRequest)
			return
		}
		defer r.Body.Close()

		// Проверяем подпись и получаем данные
		webhookData, err := h.client.VerifyWebhook(body, "")
		if err != nil {
			slog.Error("webhook: signature verification error", "error", err)
			http.Error(w, "invalid signature", http.StatusUnauthorized)
			return
		}

		slog.Info("Получен webhook от Cryptomus",
			"order_id", webhookData.OrderID,
			"status", webhookData.Status,
			"amount", webhookData.Amount,
			"payment_amount", webhookData.PaymentAmount,
		)

		// Обрабатываем платеж
		if err := h.processPayment(ctx, webhookData); err != nil {
			slog.Error("webhook: process payment error", "error", err, "order_id", webhookData.OrderID)
			http.Error(w, "internal server error", http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
	})
}

// processPayment обрабатывает платеж на основе данных webhook'а
func (h *WebhookHandler) processPayment(ctx context.Context, data *WebhookData) error {
	// Извлекаем ID покупки из order_id
	// Ожидаем формат: "purchase_123"
	if !strings.HasPrefix(data.OrderID, "purchase_") {
		return nil // Игнорируем неизвестные order_id
	}

	purchaseIDStr := strings.TrimPrefix(data.OrderID, "purchase_")
	purchaseID, err := strconv.ParseInt(purchaseIDStr, 10, 64)
	if err != nil {
		slog.Error("Failed to parse purchase ID", "order_id", data.OrderID, "error", err)
		return nil // Игнорируем неверные ID
	}

	// Обрабатываем платеж в зависимости от статуса
	switch data.Status {
	case PaymentStatusPaid, PaymentStatusPaidOver:
		return h.handleSuccessfulPayment(ctx, purchaseID, data)
	case PaymentStatusFail, PaymentStatusCancel, PaymentStatusSystemFail:
		return h.handleFailedPayment(ctx, purchaseID, data)
	case PaymentStatusRefundPaid:
		return h.handleRefundedPayment(ctx, purchaseID, data)
	default:
		slog.Info("Получен промежуточный статус платежа",
			"order_id", data.OrderID,
			"status", data.Status,
		)
		return nil
	}
}

// handleSuccessfulPayment обрабатывает успешный платеж
func (h *WebhookHandler) handleSuccessfulPayment(ctx context.Context, purchaseID int64, data *WebhookData) error {
	slog.Info("Обработка успешного платежа",
		"purchase_id", purchaseID,
		"order_id", data.OrderID,
		"amount", data.Amount,
	)

	// Обрабатываем покупку через PaymentProcessor
	err := h.paymentProcessor.ProcessPurchaseById(ctx, purchaseID)
	if err != nil {
		slog.Error("Failed to process purchase", "purchase_id", purchaseID, "error", err)
		return err
	}

	slog.Info("Успешный платеж обработан", "purchase_id", purchaseID)
	return nil
}

// handleFailedPayment обрабатывает неудачный платеж
func (h *WebhookHandler) handleFailedPayment(ctx context.Context, purchaseID int64, data *WebhookData) error {
	slog.Info("Обработка неудачного платежа",
		"purchase_id", purchaseID,
		"order_id", data.OrderID,
		"status", data.Status,
	)

	// Здесь можно добавить логику обработки неудачных платежей
	// Например, обновление статуса покупки на "failed"

	return nil
}

// handleRefundedPayment обрабатывает возврат платежа
func (h *WebhookHandler) handleRefundedPayment(ctx context.Context, purchaseID int64, data *WebhookData) error {
	slog.Info("Обработка возврата платежа",
		"purchase_id", purchaseID,
		"order_id", data.OrderID,
	)

	// Здесь можно добавить логику обработки возвратов
	// Например, деактивация подписки и уведомление пользователя

	return nil
}

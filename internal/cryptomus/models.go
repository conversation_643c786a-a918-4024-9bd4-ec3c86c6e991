package cryptomus

import "time"

// InvoiceRequest представляет запрос на создание инвойса в Cryptomus
type InvoiceRequest struct {
	Amount       string `json:"amount"`                  // Сумма к оплате
	Currency     string `json:"currency"`                // Код валюты
	OrderID      string `json:"order_id"`                // ID заказа в вашей системе
	Network      string `json:"network,omitempty"`       // Код блокчейн сети
	URLReturn    string `json:"url_return,omitempty"`    // URL для возврата на сайт
	URLSuccess   string `json:"url_success,omitempty"`   // URL после успешной оплаты
	URLCallback  string `json:"url_callback,omitempty"`  // URL для webhook'ов
	Lifetime     int    `json:"lifetime,omitempty"`      // Время жизни инвойса в секундах
	ToCurrency   string `json:"to_currency,omitempty"`   // Валюта для конвертации
	Description  string `json:"description,omitempty"`   // Описание платежа
	AdditionalData string `json:"additional_data,omitempty"` // Дополнительные данные
}

// InvoiceResponse представляет ответ при создании инвойса
type InvoiceResponse struct {
	UUID           string    `json:"uuid"`            // UUID инвойса
	OrderID        string    `json:"order_id"`        // ID заказа
	Amount         string    `json:"amount"`          // Сумма инвойса
	PaymentAmount  *string   `json:"payment_amount"`  // Сумма, оплаченная клиентом
	PayerAmount    *string   `json:"payer_amount"`    // Сумма к оплате клиентом
	PayerCurrency  *string   `json:"payer_currency"`  // Валюта оплаты клиента
	Currency       string    `json:"currency"`        // Валюта инвойса
	MerchantAmount *string   `json:"merchant_amount"` // Сумма к зачислению мерчанту
	Network        *string   `json:"network"`         // Сеть блокчейна
	Address        *string   `json:"address"`         // Адрес кошелька для оплаты
	From           *string   `json:"from"`            // Адрес отправителя
	TxID           *string   `json:"txid"`            // Хеш транзакции
	PaymentStatus  string    `json:"payment_status"`  // Статус платежа
	URL            string    `json:"url"`             // URL страницы оплаты
	ExpiredAt      int64     `json:"expired_at"`      // Время истечения (timestamp)
	IsFinal        bool      `json:"is_final"`        // Финализирован ли инвойс
	AdditionalData *string   `json:"additional_data"` // Дополнительные данные
	CreatedAt      string    `json:"created_at"`      // Дата создания
	UpdatedAt      string    `json:"updated_at"`      // Дата обновления
}

// IsPaid проверяет, оплачен ли инвойс
func (r InvoiceResponse) IsPaid() bool {
	return r.PaymentStatus == "paid"
}

// IsExpired проверяет, истек ли инвойс
func (r InvoiceResponse) IsExpired() bool {
	return time.Now().Unix() > r.ExpiredAt
}

// PaymentInfoRequest представляет запрос информации о платеже
type PaymentInfoRequest struct {
	UUID    string `json:"uuid,omitempty"`     // UUID платежа
	OrderID string `json:"order_id,omitempty"` // ID заказа
}

// WebhookData представляет данные webhook'а от Cryptomus
type WebhookData struct {
	Type               string  `json:"type"`                 // Тип (payment/wallet)
	UUID               string  `json:"uuid"`                 // UUID платежа
	OrderID            string  `json:"order_id"`             // ID заказа
	Amount             string  `json:"amount"`               // Сумма инвойса
	PaymentAmount      string  `json:"payment_amount"`       // Сумма, оплаченная клиентом
	PaymentAmountUSD   string  `json:"payment_amount_usd"`   // Сумма в USD
	MerchantAmount     string  `json:"merchant_amount"`      // Сумма к зачислению
	Commission         string  `json:"commission"`           // Комиссия Cryptomus
	IsFinal            bool    `json:"is_final"`             // Финализирован ли инвойс
	Status             string  `json:"status"`               // Статус платежа
	From               *string `json:"from"`                 // Адрес отправителя
	WalletAddressUUID  *string `json:"wallet_address_uuid"`  // UUID статического кошелька
	Network            string  `json:"network"`              // Сеть блокчейна
	Currency           string  `json:"currency"`             // Валюта инвойса
	PayerCurrency      string  `json:"payer_currency"`       // Валюта оплаты
	AdditionalData     *string `json:"additional_data"`      // Дополнительные данные
	TxID               *string `json:"txid"`                 // Хеш транзакции
	Sign               string  `json:"sign"`                 // Подпись
}

// APIResponse представляет общий ответ от API Cryptomus
type APIResponse[T any] struct {
	State  int `json:"state"`  // Код состояния (0 = успех)
	Result T   `json:"result"` // Результат
}

// ErrorResponse представляет ответ с ошибкой
type ErrorResponse struct {
	State   int                    `json:"state"`
	Message string                 `json:"message,omitempty"`
	Errors  map[string]interface{} `json:"errors,omitempty"`
}

// PaymentStatus содержит возможные статусы платежей
const (
	PaymentStatusCheck        = "check"         // Ожидание оплаты
	PaymentStatusConfirmCheck = "confirm_check" // Подтверждение оплаты
	PaymentStatusPaid         = "paid"          // Оплачено
	PaymentStatusPaidOver     = "paid_over"     // Переплачено
	PaymentStatusFail         = "fail"          // Неудача
	PaymentStatusWrongAmount  = "wrong_amount"  // Неверная сумма
	PaymentStatusCancel       = "cancel"        // Отменено
	PaymentStatusSystemFail   = "system_fail"   // Системная ошибка
	PaymentStatusRefundProcess = "refund_process" // Возврат в процессе
	PaymentStatusRefundFail   = "refund_fail"   // Возврат неудачен
	PaymentStatusRefundPaid   = "refund_paid"   // Возврат выполнен
)

package cryptomus

import (
	"bytes"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

// CryptomusAPI определяет интерфейс для работы с Cryptomus API
type CryptomusAPI interface {
	CreateInvoice(req *InvoiceRequest) (*InvoiceResponse, error)
	GetPaymentInfo(req *PaymentInfoRequest) (*InvoiceResponse, error)
	VerifyWebhook(data []byte, signature string) (*WebhookData, error)
}

// Client представляет клиент для работы с Cryptomus API
type Client struct {
	httpClient *http.Client
	baseURL    string
	merchantID string
	apiKey     string
}

// NewCryptomusClient создает новый клиент Cryptomus
func NewCryptomusClient(merchantID, apiKey string) *Client {
	return &Client{
		httpClient: &http.Client{},
		baseURL:    "https://api.cryptomus.com/v1",
		merchantID: merchantID,
		apiKey:     apiKey,
	}
}

// generateSign создает подпись для запроса
func (c *Client) generateSign(data []byte) string {
	encoded := base64.StdEncoding.EncodeToString(data)
	hash := md5.Sum([]byte(encoded + c.apiKey))
	return fmt.Sprintf("%x", hash)
}

// makeRequest выполняет HTTP запрос к API
func (c *Client) makeRequest(method, endpoint string, payload interface{}) ([]byte, error) {
	var body []byte
	var err error

	if payload != nil {
		body, err = json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("ошибка сериализации запроса: %w", err)
		}
	}

	url := fmt.Sprintf("%s%s", c.baseURL, endpoint)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("ошибка создания запроса: %w", err)
	}

	// Устанавливаем заголовки
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("merchant", c.merchantID)
	
	if body != nil {
		sign := c.generateSign(body)
		req.Header.Set("sign", sign)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("ошибка выполнения запроса: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("ошибка чтения ответа: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API вернул ошибку. Статус: %d, Тело: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

// CreateInvoice создает новый инвойс для оплаты
func (c *Client) CreateInvoice(req *InvoiceRequest) (*InvoiceResponse, error) {
	respBody, err := c.makeRequest("POST", "/payment", req)
	if err != nil {
		return nil, fmt.Errorf("ошибка создания инвойса: %w", err)
	}

	var apiResp APIResponse[InvoiceResponse]
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return nil, fmt.Errorf("ошибка десериализации ответа: %w", err)
	}

	if apiResp.State != 0 {
		// Попробуем распарсить как ошибку
		var errResp ErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err == nil {
			return nil, fmt.Errorf("API вернул ошибку: %s (код: %d)", errResp.Message, errResp.State)
		}
		return nil, fmt.Errorf("API вернул ошибку с кодом: %d", apiResp.State)
	}

	return &apiResp.Result, nil
}

// GetPaymentInfo получает информацию о платеже
func (c *Client) GetPaymentInfo(req *PaymentInfoRequest) (*InvoiceResponse, error) {
	respBody, err := c.makeRequest("POST", "/payment/info", req)
	if err != nil {
		return nil, fmt.Errorf("ошибка получения информации о платеже: %w", err)
	}

	var apiResp APIResponse[InvoiceResponse]
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return nil, fmt.Errorf("ошибка десериализации ответа: %w", err)
	}

	if apiResp.State != 0 {
		var errResp ErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err == nil {
			return nil, fmt.Errorf("API вернул ошибку: %s (код: %d)", errResp.Message, errResp.State)
		}
		return nil, fmt.Errorf("API вернул ошибку с кодом: %d", apiResp.State)
	}

	return &apiResp.Result, nil
}

// VerifyWebhook проверяет подпись webhook'а и возвращает данные
func (c *Client) VerifyWebhook(data []byte, signature string) (*WebhookData, error) {
	var webhookData WebhookData
	if err := json.Unmarshal(data, &webhookData); err != nil {
		return nil, fmt.Errorf("ошибка десериализации webhook данных: %w", err)
	}

	// Извлекаем подпись из данных
	receivedSign := webhookData.Sign
	
	// Создаем копию данных без подписи для проверки
	var dataWithoutSign map[string]interface{}
	if err := json.Unmarshal(data, &dataWithoutSign); err != nil {
		return nil, fmt.Errorf("ошибка парсинга данных для проверки подписи: %w", err)
	}
	
	// Удаляем подпись из данных
	delete(dataWithoutSign, "sign")
	
	// Сериализуем данные без подписи
	dataForSign, err := json.Marshal(dataWithoutSign)
	if err != nil {
		return nil, fmt.Errorf("ошибка сериализации данных для проверки: %w", err)
	}

	// Генерируем ожидаемую подпись
	expectedSign := c.generateSign(dataForSign)

	// Сравниваем подписи
	if !strings.EqualFold(expectedSign, receivedSign) {
		return nil, fmt.Errorf("неверная подпись webhook'а: ожидалось %s, получено %s", expectedSign, receivedSign)
	}

	return &webhookData, nil
}

// IsPaymentSuccessful проверяет, является ли платеж успешным
func IsPaymentSuccessful(status string) bool {
	return status == PaymentStatusPaid || status == PaymentStatusPaidOver
}

// IsPaymentFinal проверяет, является ли статус платежа финальным
func IsPaymentFinal(status string) bool {
	finalStatuses := []string{
		PaymentStatusPaid,
		PaymentStatusPaidOver,
		PaymentStatusFail,
		PaymentStatusCancel,
		PaymentStatusSystemFail,
		PaymentStatusRefundPaid,
	}
	
	for _, finalStatus := range finalStatuses {
		if status == finalStatus {
			return true
		}
	}
	return false
}

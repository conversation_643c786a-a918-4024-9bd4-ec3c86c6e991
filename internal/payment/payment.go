package payment

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/cache"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/cryptomus"
	"remnawave-tg-shop-bot/internal/cryptopay"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/remnawave"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/internal/yookasa"
	"remnawave-tg-shop-bot/utils"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// formatMoscowTime форматирует время в московском часовом поясе с корректным смещением UTC
func formatMoscowTime(t time.Time) string {
	moscowLocation, _ := time.LoadLocation("Europe/Moscow")
	moscowTime := t.In(moscowLocation)

	// Получаем смещение от UTC для московского времени
	_, offset := moscowTime.Zone()
	hours := offset / 3600

	// Форматируем время с корректным смещением UTC
	return moscowTime.Format("02.01.2006 15:04") + fmt.Sprintf(" (UTC%+d)", hours)
}

// getPaymentMethodName возвращает название способа оплаты с эмодзи
func getPaymentMethodName(invoiceType database.InvoiceType) string {
	switch invoiceType {
	case database.InvoiceTypeYookasa:
		return "💳 Банковская карта"
	case database.InvoiceTypeTelegram:
		return "⭐ Telegram Stars"
	case database.InvoiceTypeCrypto:
		return "₿ Криптовалюта"
	case database.InvoiceTypeTribute:
		return "💰 Tribute"
	case database.InvoiceTypeCryptomus:
		return "🔐 Cryptomus"
	default:
		return "❓ Неизвестный способ"
	}
}

type PaymentService struct {
	purchaseRepository *database.PurchaseRepository
	remnawaveClient    *remnawave.Client
	customerRepository *database.CustomerRepository
	telegramBot        *bot.Bot
	translation        *translation.Manager
	cryptoPayClient    *cryptopay.Client
	yookasaClient      *yookasa.Client
	cryptomusClient    *cryptomus.Client
	referralRepository *database.ReferralRepository
	cache              *cache.Cache
	promoCodeService   *service.PromoCodeService
	tariffRepository   *database.TariffRepository
}

func NewPaymentService(
	translation *translation.Manager,
	purchaseRepository *database.PurchaseRepository,
	remnawaveClient *remnawave.Client,
	customerRepository *database.CustomerRepository,
	telegramBot *bot.Bot,
	cryptoPayClient *cryptopay.Client,
	yookasaClient *yookasa.Client,
	cryptomusClient *cryptomus.Client,
	referralRepository *database.ReferralRepository,
	cache *cache.Cache,
	promoCodeService *service.PromoCodeService,
	tariffRepository *database.TariffRepository,
) *PaymentService {
	return &PaymentService{
		purchaseRepository: purchaseRepository,
		remnawaveClient:    remnawaveClient,
		customerRepository: customerRepository,
		telegramBot:        telegramBot,
		translation:        translation,
		cryptoPayClient:    cryptoPayClient,
		yookasaClient:      yookasaClient,
		cryptomusClient:    cryptomusClient,
		referralRepository: referralRepository,
		cache:              cache,
		promoCodeService:   promoCodeService,
		tariffRepository:   tariffRepository,
	}
}

func (s PaymentService) ProcessPurchaseById(ctx context.Context, purchaseId int64) error {
	purchase, err := s.purchaseRepository.FindById(ctx, purchaseId)
	if err != nil {
		return err
	}
	if purchase == nil {
		return fmt.Errorf("purchase with crypto invoice id %d not found", utils.MaskHalfInt64(purchaseId))
	}

	customer, err := s.customerRepository.FindById(ctx, purchase.CustomerID)
	if err != nil {
		return err
	}
	if customer == nil {
		return fmt.Errorf("customer %s not found", utils.MaskHalfInt64(purchase.CustomerID))
	}

	if messageId, b := s.cache.GetInt(purchase.ID); b {
		_, err = s.telegramBot.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    customer.TelegramID,
			MessageID: messageId,
		})
		if err != nil {
			slog.Error("Error deleting message", err)
		}
	}

	// Получаем тариф для определения количества дней и лимита трафика
	var days int
	var trafficLimitBytes int

	if purchase.TariffCode != nil && *purchase.TariffCode != "" {
		// Используем новые поля тарифа
		tariff, err := s.tariffRepository.GetByCode(ctx, *purchase.TariffCode)
		if err != nil {
			slog.Error("Ошибка получения тарифа", "tariffCode", *purchase.TariffCode, "error", err)
			// Fallback к старой логике - безлимитный трафик
			days = purchase.Month * 30
			trafficLimitBytes = 0
		} else if tariff != nil {
			days = tariff.Days
			trafficLimitBytes = tariff.GetTrafficLimitBytes()
			// Если trafficLimitBytes = 0, это означает безлимитный трафик (оставляем как есть)
		} else {
			// Тариф не найден - используем старую логику с безлимитным трафиком
			days = purchase.Month * 30
			trafficLimitBytes = 0
		}
	} else {
		// Старая покупка без кода тарифа - используем старую логику с безлимитным трафиком
		days = purchase.Month * 30
		trafficLimitBytes = 0
	}

	user, err := s.remnawaveClient.CreateOrUpdateUser(ctx, customer.ID, customer.TelegramID, trafficLimitBytes, days)
	if err != nil {
		return err
	}

	err = s.purchaseRepository.MarkAsPaid(ctx, purchase.ID)
	if err != nil {
		return err
	}

	customerFilesToUpdate := map[string]interface{}{
		"subscription_link":         user.SubscriptionUrl,
		"expire_at":                 user.ExpireAt,
		"last_notification_sent_at": nil,  // Сбрасываем время последнего уведомления
		"notification_days_sent":    "[]", // Сбрасываем список отправленных уведомлений
	}

	err = s.customerRepository.UpdateFields(ctx, customer.ID, customerFilesToUpdate)
	if err != nil {
		return err
	}

	// Обновляем объект customer в памяти для корректной работы createConnectKeyboard
	customer.SubscriptionLink = &user.SubscriptionUrl
	customer.ExpireAt = &user.ExpireAt

	// Применяем промокод, если он был указан при создании покупки
	if purchase.PromoCode != nil && *purchase.PromoCode != "" && purchase.TariffCode != nil && *purchase.TariffCode != "" {
		err = s.applyPromoCodeToPurchase(ctx, purchase, customer)
		if err != nil {
			slog.Error("Ошибка применения промокода к оплаченной покупке", "error", err, "purchaseId", purchase.ID, "promoCode", *purchase.PromoCode)
			// Не возвращаем ошибку, так как покупка уже обработана
		}
	}

	// Формируем улучшенное сообщение об активации подписки
	activationText := s.buildActivationMessage(ctx, purchase, customer)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    customer.TelegramID,
		Text:      activationText,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: s.createConnectKeyboard(customer),
		},
	})
	if err != nil {
		return err
	}

	ctxReferee := context.Background()
	referee, err := s.referralRepository.FindByReferee(ctxReferee, customer.TelegramID)
	if referee == nil {
		return nil
	}
	if referee.BonusGranted {
		return nil
	}
	if err != nil {
		return err
	}
	refereeCustomer, err := s.customerRepository.FindByTelegramId(ctxReferee, referee.ReferrerID)
	if err != nil {
		return err
	}
	// Реферальный бонус - безлимитный трафик
	refereeUser, err := s.remnawaveClient.CreateOrUpdateUser(ctxReferee, refereeCustomer.ID, refereeCustomer.TelegramID, 0, config.GetReferralDays())
	if err != nil {
		return err
	}
	refereeUserFilesToUpdate := map[string]interface{}{
		"subscription_link": refereeUser.GetSubscriptionUrl(),
		"expire_at":         refereeUser.GetExpireAt(),
	}
	err = s.customerRepository.UpdateFields(ctxReferee, refereeCustomer.ID, refereeUserFilesToUpdate)
	if err != nil {
		return err
	}
	err = s.referralRepository.MarkBonusGranted(ctxReferee, referee.ID)
	if err != nil {
		return err
	}
	slog.Info("Granted referral bonus", "customer_id", utils.MaskHalfInt64(refereeCustomer.ID))
	_, err = s.telegramBot.SendMessage(ctxReferee, &bot.SendMessageParams{
		ChatID:    refereeCustomer.TelegramID,
		ParseMode: models.ParseModeHTML,
		Text:      s.translation.GetText(refereeCustomer.Language, "referral_bonus_granted"),
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: s.createConnectKeyboard(refereeCustomer),
		},
	})
	slog.Info("purchase processed", "purchase_id", utils.MaskHalfInt64(purchase.ID), "type", purchase.InvoiceType, "customer_id", utils.MaskHalfInt64(customer.ID))

	return nil
}

func (s PaymentService) createConnectKeyboard(customer *database.Customer) [][]models.InlineKeyboardButton {
	var inlineCustomerKeyboard [][]models.InlineKeyboardButton
	var url string
	if customer != nil && customer.SubscriptionLink != nil && *customer.SubscriptionLink != "" {
		url = *customer.SubscriptionLink
	} else if config.GetMiniAppURL() != "" {
		url = config.GetMiniAppURL()
	}
	if url != "" {
		inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
			{Text: s.translation.GetText(customer.Language, "connect_button"), WebApp: &models.WebAppInfo{URL: url}},
		})
	} else {
		inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
			{Text: s.translation.GetText(customer.Language, "connect_button"), CallbackData: "connect"},
		})
	}
	inlineCustomerKeyboard = append(inlineCustomerKeyboard, []models.InlineKeyboardButton{
		{Text: s.translation.GetText(customer.Language, "back_button"), CallbackData: "start"},
	})
	return inlineCustomerKeyboard
}

func (s PaymentService) CreatePurchase(ctx context.Context, amount int, months int, customer *database.Customer, invoiceType database.InvoiceType) (url string, purchaseId int64, err error) {
	// Метод CreatePurchase не поддерживает промокоды и тарифы, передаем пустые строки
	switch invoiceType {
	case database.InvoiceTypeCrypto:
		return s.createCryptoInvoice(ctx, amount, months, customer, "", "", "")
	case database.InvoiceTypeYookasa:
		return s.createYookasaInvoice(ctx, amount, months, customer, "", "", "")
	case database.InvoiceTypeTelegram:
		return s.createTelegramInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeTribute:
		return s.createTributeInvoice(ctx, amount, months, customer, "", "")
	case database.InvoiceTypeCryptomus:
		return s.createCryptomusInvoice(ctx, amount, months, customer, "", "", "")
	default:
		return "", 0, fmt.Errorf("unknown invoice type: %s", invoiceType)
	}
}

func (s PaymentService) CreatePurchaseByTariff(ctx context.Context, tariff *database.Tariff, customer *database.Customer, invoiceType database.InvoiceType, promoCode string) (url string, purchaseId int64, err error) {
	var amount int
	// Используем количество дней из тарифа для расчета месяцев (для обратной совместимости)
	months := tariff.GetMonthsFromCode()

	// Определяем базовую сумму в зависимости от типа оплаты
	var originalAmount int
	var currency string
	switch invoiceType {
	case database.InvoiceTypeTelegram:
		originalAmount = tariff.PriceStars
		currency = "STARS"
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute, database.InvoiceTypeCryptomus:
		originalAmount = tariff.PriceRUB
		currency = "RUB"
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	// Применяем промокод, если он указан
	amount = originalAmount
	var validPromoCode *database.PromoCode
	if promoCode != "" && s.promoCodeService != nil {
		// Валидируем промокод
		validatedPromoCode, err := s.promoCodeService.ValidatePromoCode(ctx, promoCode, customer, tariff)
		if err != nil {
			slog.Error("Ошибка валидации промокода", "error", err, "code", promoCode)
			// Продолжаем без промокода, если валидация не прошла
		} else {
			validPromoCode = validatedPromoCode
			// Рассчитываем цену со скидкой
			_, amount = s.promoCodeService.CalculateDiscountedPrice(originalAmount, validPromoCode.DiscountPercent, currency)
		}
	}

	switch invoiceType {
	case database.InvoiceTypeCrypto:
		url, purchaseId, err = s.createCryptoInvoice(ctx, amount, months, customer, promoCode, tariff.Code, tariff.Title)
	case database.InvoiceTypeYookasa:
		url, purchaseId, err = s.createYookasaInvoice(ctx, amount, months, customer, promoCode, tariff.Code, tariff.Title)
	case database.InvoiceTypeTelegram:
		url, purchaseId, err = s.createTelegramInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeTribute:
		url, purchaseId, err = s.createTributeInvoice(ctx, amount, months, customer, promoCode, tariff.Code)
	case database.InvoiceTypeCryptomus:
		url, purchaseId, err = s.createCryptomusInvoice(ctx, amount, months, customer, promoCode, tariff.Code, tariff.Title)
	default:
		return "", 0, fmt.Errorf("неизвестный тип оплаты: %s", invoiceType)
	}

	if err != nil {
		return "", 0, err
	}

	// TODO: Применение промокода должно происходить только после успешной оплаты
	// Информация о промокоде должна сохраняться в purchase или передаваться через payload

	return url, purchaseId, nil
}

func (s PaymentService) createCryptoInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string, tariffTitle string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeCrypto,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", err)
		return "", 0, err
	}

	// Формируем описание на основе названия тарифа или количества месяцев
	var description string
	if tariffTitle != "" {
		// Используем название тарифа с переводом
		description = fmt.Sprintf(s.translation.GetText(customer.Language, "subscription_for_tariff"), tariffTitle)
	} else {
		// Fallback для старых тарифов без названия
		description = fmt.Sprintf(s.translation.GetText(customer.Language, "subscription_for_months"), months)
	}

	invoice, err := s.cryptoPayClient.CreateInvoice(&cryptopay.InvoiceRequest{
		CurrencyType:   "fiat",
		Fiat:           "RUB",
		Amount:         fmt.Sprintf("%d", amount),
		AcceptedAssets: "USDT",
		Payload:        fmt.Sprintf("purchaseId=%d&username=%s", purchaseId, ctx.Value("username")),
		Description:    description,
		PaidBtnName:    "callback",
		PaidBtnUrl:     config.BotURL(),
	})
	if err != nil {
		slog.Error("Error creating invoice", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"crypto_invoice_url": invoice.BotInvoiceUrl,
		"crypto_invoice_id":  invoice.InvoiceID,
		"status":             database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", err)
		return "", 0, err
	}

	return invoice.BotInvoiceUrl, purchaseId, nil
}

func (s PaymentService) createYookasaInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string, tariffTitle string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeYookasa,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", err)
		return "", 0, err
	}

	invoice, err := s.yookasaClient.CreateInvoice(ctx, amount, months, customer.ID, purchaseId, tariffTitle)
	if err != nil {
		slog.Error("Error creating invoice", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"yookasa_url": invoice.Confirmation.ConfirmationURL,
		"yookasa_id":  invoice.ID,
		"status":      database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", err)
		return "", 0, err
	}

	return invoice.Confirmation.ConfirmationURL, purchaseId, nil
}

func (s PaymentService) createCryptomusInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string, tariffTitle string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeCryptomus,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", err)
		return "", 0, err
	}

	// Формируем описание на основе названия тарифа или количества месяцев
	var description string
	if tariffTitle != "" {
		// Используем название тарифа с переводом
		description = fmt.Sprintf(s.translation.GetText(customer.Language, "subscription_for_tariff"), tariffTitle)
	} else {
		// Fallback для старых тарифов без названия
		description = fmt.Sprintf(s.translation.GetText(customer.Language, "subscription_for_months"), months)
	}

	// Создаем инвойс через Cryptomus API
	invoice, err := s.cryptomusClient.CreateInvoice(&cryptomus.InvoiceRequest{
		Amount:         fmt.Sprintf("%d", amount),
		Currency:       "RUB",
		OrderID:        fmt.Sprintf("purchase_%d", purchaseId),
		URLCallback:    fmt.Sprintf("%s/webhook/cryptomus", config.BotURL()),
		URLReturn:      config.BotURL(),
		URLSuccess:     config.BotURL(),
		Description:    description,
		AdditionalData: fmt.Sprintf("purchaseId=%d&username=%s", purchaseId, ctx.Value("username")),
		Lifetime:       3600, // 1 час
	})
	if err != nil {
		slog.Error("Error creating Cryptomus invoice", err)
		return "", 0, err
	}

	updates := map[string]interface{}{
		"cryptomus_url":  invoice.URL,
		"cryptomus_uuid": invoice.UUID,
		"status":         database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", err)
		return "", 0, err
	}

	return invoice.URL, purchaseId, nil
}

func (s PaymentService) createTelegramInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTelegram,
		Status:      database.PurchaseStatusNew,
		Amount:      float64(amount),
		Currency:    "STARS",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", err)
		return "", 0, nil
	}

	invoiceUrl, err := s.telegramBot.CreateInvoiceLink(ctx, &bot.CreateInvoiceLinkParams{
		Title:    s.translation.GetText(customer.Language, "invoice_title"),
		Currency: "XTR",
		Prices: []models.LabeledPrice{
			{
				Label:  s.translation.GetText(customer.Language, "invoice_label"),
				Amount: amount,
			},
		},
		Description: s.translation.GetText(customer.Language, "invoice_description"),
		Payload:     fmt.Sprintf("%d&%s", purchaseId, ctx.Value("username")),
	})

	updates := map[string]interface{}{
		"status": database.PurchaseStatusPending,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, updates)
	if err != nil {
		slog.Error("Error updating purchase", err)
		return "", 0, err
	}

	return invoiceUrl, purchaseId, nil
}

func (s PaymentService) ActivateTrial(ctx context.Context, telegramId int64) (string, error) {
	if config.TrialDays() == 0 {
		return "", nil
	}
	customer, err := s.customerRepository.FindByTelegramId(ctx, telegramId)
	if err != nil {
		slog.Error("Error finding customer", err)
		return "", err
	}
	if customer == nil {
		return "", fmt.Errorf("customer %d not found", telegramId)
	}
	user, err := s.remnawaveClient.CreateOrUpdateUser(ctx, customer.ID, telegramId, config.TrialTrafficLimit(), config.TrialDays())
	if err != nil {
		slog.Error("Error creating user", err)
		return "", err
	}

	customerFilesToUpdate := map[string]interface{}{
		"subscription_link": user.GetSubscriptionUrl(),
		"expire_at":         user.GetExpireAt(),
	}

	err = s.customerRepository.UpdateFields(ctx, customer.ID, customerFilesToUpdate)
	if err != nil {
		return "", err
	}

	return user.GetSubscriptionUrl(), nil

}

func (s PaymentService) CancelPayment(purchaseId int64) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	purchase, err := s.purchaseRepository.FindById(ctx, purchaseId)
	if err != nil {
		return err
	}
	if purchase == nil {
		return fmt.Errorf("purchase with crypto invoice id %d not found", utils.MaskHalfInt64(purchaseId))
	}

	purchaseFieldsToUpdate := map[string]interface{}{
		"status": database.PurchaseStatusCancel,
	}

	err = s.purchaseRepository.UpdateFields(ctx, purchaseId, purchaseFieldsToUpdate)
	if err != nil {
		return err
	}

	return nil
}

// applyPromoCodeToPurchase применяет промокод к оплаченной покупке
func (s PaymentService) applyPromoCodeToPurchase(ctx context.Context, purchase *database.Purchase, customer *database.Customer) error {
	if s.promoCodeService == nil {
		return fmt.Errorf("promoCodeService не инициализирован")
	}

	// Получаем промокод из базы данных
	promoCode, err := s.promoCodeService.GetPromoCodeByCode(ctx, *purchase.PromoCode)
	if err != nil {
		return fmt.Errorf("ошибка получения промокода: %w", err)
	}
	if promoCode == nil {
		return fmt.Errorf("промокод %s не найден", *purchase.PromoCode)
	}

	// Получаем тариф из базы данных
	tariff, err := s.tariffRepository.GetByCode(ctx, *purchase.TariffCode)
	if err != nil {
		return fmt.Errorf("ошибка получения тарифа: %w", err)
	}
	if tariff == nil {
		return fmt.Errorf("тариф %s не найден", *purchase.TariffCode)
	}

	// Определяем валюту на основе типа инвойса
	var currency string
	switch purchase.InvoiceType {
	case database.InvoiceTypeTelegram:
		currency = "STARS"
	case database.InvoiceTypeYookasa, database.InvoiceTypeCrypto, database.InvoiceTypeTribute:
		currency = "RUB"
	default:
		return fmt.Errorf("неизвестный тип инвойса: %s", purchase.InvoiceType)
	}

	// Применяем промокод
	_, err = s.promoCodeService.ApplyPromoCode(ctx, promoCode, customer, tariff, purchase.ID, currency)
	if err != nil {
		return fmt.Errorf("ошибка применения промокода: %w", err)
	}

	slog.Info("Промокод успешно применен к покупке", "purchaseId", purchase.ID, "promoCode", *purchase.PromoCode, "customerId", customer.ID)
	return nil
}

func (s PaymentService) createTributeInvoice(ctx context.Context, amount int, months int, customer *database.Customer, promoCode string, tariffCode string) (url string, purchaseId int64, err error) {
	var promoCodePtr *string
	if promoCode != "" {
		promoCodePtr = &promoCode
	}

	var tariffCodePtr *string
	if tariffCode != "" {
		tariffCodePtr = &tariffCode
	}

	purchaseId, err = s.purchaseRepository.Create(ctx, &database.Purchase{
		InvoiceType: database.InvoiceTypeTribute,
		Status:      database.PurchaseStatusPending,
		Amount:      float64(amount),
		Currency:    "RUB",
		CustomerID:  customer.ID,
		Month:       months,
		PromoCode:   promoCodePtr,
		TariffCode:  tariffCodePtr,
	})
	if err != nil {
		slog.Error("Error creating purchase", err)
		return "", 0, err
	}

	return "", purchaseId, nil
}

// buildActivationMessage формирует улучшенное сообщение об активации подписки
func (s PaymentService) buildActivationMessage(ctx context.Context, purchase *database.Purchase, customer *database.Customer) string {
	// Базовое сообщение
	baseMessage := "🎉 <b>Подписка успешно активирована!</b>\n\n"

	// Получаем информацию о тарифе, если доступна
	var tariffInfo string
	if purchase.TariffCode != nil && *purchase.TariffCode != "" {
		tariff, err := s.tariffRepository.GetByCode(ctx, *purchase.TariffCode)
		if err == nil && tariff != nil {
			tariffInfo = fmt.Sprintf("📦 <b>Тариф:</b> %s\n", tariff.Title)
		}
	}

	// Информация о способе оплаты
	paymentMethodInfo := fmt.Sprintf("💳 <b>Способ оплаты:</b> %s\n", getPaymentMethodName(purchase.InvoiceType))

	// Информация о дате окончания подписки
	var expirationInfo string
	if customer.ExpireAt != nil {
		expirationInfo = fmt.Sprintf("⏰ <b>Ваша подписка действует до:</b> %s\n", formatMoscowTime(*customer.ExpireAt))
	}

	// Информация о промокоде, если был применен
	var promoInfo string
	if purchase.PromoCode != nil && *purchase.PromoCode != "" {
		promoInfo = fmt.Sprintf("🎟️ <b>Применен промокод:</b> %s\n", *purchase.PromoCode)
	}

	// Собираем итоговое сообщение
	message := baseMessage + tariffInfo + paymentMethodInfo + expirationInfo + promoInfo

	// Добавляем призыв к действию
	message += "\n✨ Спасибо за покупку! Теперь вы можете подключиться к VPN."

	return message
}

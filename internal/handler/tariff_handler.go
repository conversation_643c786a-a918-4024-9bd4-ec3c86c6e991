package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"strconv"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

const (
	tariffFSMStateCode           = 4001
	tariffFSMStateTitle          = 4002
	tariffFSMStatePriceRUB       = 4003
	tariffFSMStatePriceStars     = 4004
	tariffFSMStateDays           = 4005 // Новое состояние для ввода количества дней
	tariffFSMStateTrafficLimit   = 4006 // Новое состояние для ввода лимита трафика
	tariffFSMStateConfirm        = 4007 // Подтверждение создания тарифа
	tariffFSMStateEditTitle      = 4008
	tariffFSMStateEditPriceRUB   = 4009
	tariffFSMStateEditPriceStars = 4010
	tariffFSMStateEditConfirm    = 4011
)

type TariffHandler struct {
	*AdminHandler
}

func NewTariffHandler(adminHandler *AdminHandler) *TariffHandler {
	return &TariffHandler{AdminHandler: adminHandler}
}

func (h *TariffHandler) AdminTariffTextHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	slog.Info("[AdminTariffTextHandler] Функция вызвана", "update_exists", update != nil, "message_exists", update != nil && update.Message != nil)

	if update == nil || update.Message == nil {
		slog.Error("[AdminTariffTextHandler] update or message is nil", "update", update)
		return
	}
	userID := update.Message.From.ID
	slog.Info("[AdminTariffTextHandler] Получен userID", "user_id", userID, "text", update.Message.Text)

	state, ok := h.cache.GetInt(userID)
	if !ok {
		slog.Warn("[AdminTariffTextHandler] FSM state not found", "user_id", userID)
		return
	}
	msgID, ok := h.cache.GetInt(9999999 + userID)
	if !ok {
		slog.Warn("[AdminTariffTextHandler] message id not found", "user_id", userID)
		return
	}
	msg := update.Message

	slog.Info("[AdminTariffTextHandler] Обрабатываю состояние FSM", "user_id", userID, "state", state, "msgID", msgID, "text", msg.Text)

	switch state {
	case tariffFSMStateEditTitle:
		title := msg.Text
		if len(title) == 0 || len(title) > 64 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Название тарифа не должно быть пустым и не должно превышать 64 символа. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		h.cache.SetString(7000001+userID, title)
		h.cache.SetInt(userID, tariffFSMStateEditPriceRUB)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>новую цену в рублях</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в рублях", err)
		}
		return
	case tariffFSMStateEditPriceRUB:
		if msg.Text != "" {
			priceRUB, err := strconv.Atoi(msg.Text)
			if err != nil || priceRUB <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в рублях должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000002+userID, priceRUB)
		}
		h.cache.SetInt(userID, tariffFSMStateEditPriceStars)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>новую цену в звёздах</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в звёздах", err)
		}
		return
	case tariffFSMStateEditPriceStars:
		if msg.Text != "" {
			priceStars, err := strconv.Atoi(msg.Text)
			if err != nil || priceStars <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в звёздах должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000003+userID, priceStars)
		}
		h.cache.SetInt(userID, tariffFSMStateEditConfirm)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		code, _ := h.cache.GetString(7000000 + userID)
		title, _ := h.cache.GetString(7000001 + userID)
		priceRUB, _ := h.cache.GetInt(7000002 + userID)
		priceStars, _ := h.cache.GetInt(7000003 + userID)
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Сохранить изменения тарифа?\nКод: <code>%s</code>\nНазвание: %s\nЦена: %d₽ / %d⭐", code, title, priceRUB, priceStars),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Сохранить", CallbackData: "tariff_update"}, {Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага подтверждения редактирования тарифа", err)
		}
		return
	case tariffFSMStateCode:
		code := msg.Text
		if len(code) < 2 || len(code) > 16 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Код тарифа должен быть от 2 до 16 символов. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		for _, c := range code {
			if !(c >= 'a' && c <= 'z') && !(c >= 'A' && c <= 'Z') && !(c >= '0' && c <= '9') && c != '_' && c != '-' {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Код может содержать только латинские буквы, цифры, -, _ . Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
		}
		tariff, _ := h.tariffRepository.GetByCode(ctx, code)
		if tariff != nil {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Тариф с таким кодом уже существует. Введите другой код:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}

		h.cache.SetString(7000000+userID, code)
		h.cache.SetInt(userID, tariffFSMStateTitle)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		editedMsg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>название тарифа</b> (до 64 символов):",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода названия тарифа", err)
		} else {
			// Обновляем msgID в кеше после успешного редактирования
			h.cache.SetInt(9999999+userID, editedMsg.ID)
		}
		return
	case tariffFSMStateTitle:
		title := msg.Text
		if len(title) == 0 || len(title) > 64 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Название тарифа не должно быть пустым и не должно превышать 64 символа. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		h.cache.SetString(7000001+userID, title)
		h.cache.SetInt(userID, tariffFSMStatePriceRUB)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		editedMsg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>цену в рублях</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в рублях", err)
		} else {
			// Обновляем msgID в кеше после успешного редактирования
			h.cache.SetInt(9999999+userID, editedMsg.ID)
		}
		return
	case tariffFSMStatePriceRUB:
		if msg.Text != "" {
			priceRUB, err := strconv.Atoi(msg.Text)
			if err != nil || priceRUB <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в рублях должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000002+userID, priceRUB)
		}
		h.cache.SetInt(userID, tariffFSMStatePriceStars)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		editedMsg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>цену в звёздах</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в звёздах", err)
		} else {
			// Обновляем msgID в кеше после успешного редактирования
			h.cache.SetInt(9999999+userID, editedMsg.ID)
		}
		return
	case tariffFSMStatePriceStars:
		if msg.Text != "" {
			priceStars, err := strconv.Atoi(msg.Text)
			if err != nil || priceStars <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в звёздах должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000003+userID, priceStars)
		}
		h.cache.SetInt(userID, tariffFSMStateDays)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		editedMsg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>количество дней</b> действия тарифа (например, 30, 90, 365):",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода количества дней", err)
		} else {
			// Обновляем msgID в кеше после успешного редактирования
			h.cache.SetInt(9999999+userID, editedMsg.ID)
		}
		return
	case tariffFSMStateDays:
		slog.Info("[TARIFF FSM] Обрабатываю ввод количества дней", "user_id", userID, "text", msg.Text)
		if msg.Text != "" {
			days, err := strconv.Atoi(msg.Text)
			if err != nil || days <= 0 {
				slog.Warn("[TARIFF FSM] Некорректный ввод количества дней", "user_id", userID, "input", msg.Text, "error", err)
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Количество дней должно быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			slog.Info("[TARIFF FSM] Парсинг количества дней успешен", "user_id", userID, "input", msg.Text, "parsed", days)
			slog.Info("[TARIFF FSM] Сохраняю количество дней в кеш", "user_id", userID, "days", days)
			h.cache.SetInt(7000004+userID, days)
		}
		slog.Info("[TARIFF FSM] Переходим к состоянию ввода лимита трафика", "user_id", userID)
		h.cache.SetInt(userID, tariffFSMStateTrafficLimit)
		slog.Info("[TARIFF FSM] Состояние FSM установлено", "user_id", userID, "new_state", tariffFSMStateTrafficLimit)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		slog.Info("[TARIFF FSM] Отправляю сообщение для ввода лимита трафика", "user_id", userID, "msgID", msgID)
		editedMsg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>лимит трафика в ГБ</b> (0 = безлимитный трафик):",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("[TARIFF FSM] Ошибка шага ввода лимита трафика", "user_id", userID, "error", err)
		} else {
			// Обновляем msgID в кеше после успешного редактирования
			h.cache.SetInt(9999999+userID, editedMsg.ID)
			slog.Info("[TARIFF FSM] Сообщение для ввода лимита трафика отправлено успешно", "user_id", userID, "new_msgID", editedMsg.ID)

			// Проверяем, что состояние действительно сохранилось в кеше
			savedState, ok := h.cache.GetInt(userID)
			if ok {
				slog.Info("[TARIFF FSM] Проверка состояния в кеше после установки", "user_id", userID, "saved_state", savedState, "expected_state", tariffFSMStateTrafficLimit)
			} else {
				slog.Error("[TARIFF FSM] Состояние не найдено в кеше после установки", "user_id", userID)
			}
		}
		return
	case tariffFSMStateTrafficLimit:
		slog.Info("[TARIFF FSM] Обрабатываю ввод лимита трафика", "user_id", userID, "text", msg.Text)
		if msg.Text != "" {
			trafficLimit, err := strconv.Atoi(msg.Text)
			if err != nil || trafficLimit < 0 {
				slog.Warn("[TARIFF FSM] Некорректный ввод лимита трафика", "user_id", userID, "input", msg.Text, "error", err)
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Лимит трафика должен быть неотрицательным целым числом (0 = безлимитный). Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			slog.Info("[TARIFF FSM] Парсинг лимита трафика успешен", "user_id", userID, "input", msg.Text, "parsed", trafficLimit)
			slog.Info("[TARIFF FSM] Сохраняю лимит трафика в кеш", "user_id", userID, "traffic_limit", trafficLimit)
			h.cache.SetInt(7000005+userID, trafficLimit)
		}
		slog.Info("[TARIFF FSM] Переходим к состоянию подтверждения", "user_id", userID)
		h.cache.SetInt(userID, tariffFSMStateConfirm)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		code, _ := h.cache.GetString(7000000 + userID)
		title, _ := h.cache.GetString(7000001 + userID)
		priceRUB, _ := h.cache.GetInt(7000002 + userID)
		priceStars, _ := h.cache.GetInt(7000003 + userID)
		days, _ := h.cache.GetInt(7000004 + userID)
		trafficLimit, _ := h.cache.GetInt(7000005 + userID)
		slog.Info("[TARIFF FSM] Данные тарифа для подтверждения", "user_id", userID, "code", code, "title", title, "priceRUB", priceRUB, "priceStars", priceStars, "days", days, "trafficLimit", trafficLimit)

		var trafficText string
		if trafficLimit == 0 {
			trafficText = "безлимитный"
		} else {
			trafficText = fmt.Sprintf("%d ГБ", trafficLimit)
		}

		slog.Info("[TARIFF FSM] Отправляю сообщение подтверждения создания тарифа", "user_id", userID, "msgID", msgID)
		editedMsg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Подтвердите создание тарифа:\n\n<b>Код:</b> <code>%s</code>\n<b>Название:</b> %s\n<b>Цена:</b> %d₽ / %d⭐\n<b>Дней:</b> %d\n<b>Трафик:</b> %s", code, title, priceRUB, priceStars, days, trafficText),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Создать", CallbackData: "tariff_create"}, {Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("[TARIFF FSM] Ошибка шага подтверждения создания тарифа", "user_id", userID, "error", err)
		} else {
			// Обновляем msgID в кеше после успешного редактирования
			h.cache.SetInt(9999999+userID, editedMsg.ID)
			slog.Info("[TARIFF FSM] Сообщение подтверждения отправлено успешно", "user_id", userID, "new_msgID", editedMsg.ID)
		}
		return
	}
}

const tariffsPerPage = 3

func (h *TariffHandler) showTariffsPage(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery, page int) {
	userID := callback.From.ID
	h.clearTariffMessages(ctx, b, userID, callback.Message.Message.Chat.ID)

	tariffs, err := h.tariffRepository.GetAll(ctx, false)
	if err != nil {
		return
	}

	total := len(tariffs)
	totalPages := (total + tariffsPerPage - 1) / tariffsPerPage
	if page < 1 {
		page = 1
	}
	if page > totalPages {
		page = totalPages
	}

	start := (page - 1) * tariffsPerPage
	end := start + tariffsPerPage
	if end > total {
		end = total
	}

	pagedTariffs := tariffs[start:end]

	var sentMessages []int

	if len(pagedTariffs) == 0 {
		text := "Нет созданных тарифов."
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "➕ Создать", CallbackData: "tariff_add"}},
			{{Text: "⬅️ Назад", CallbackData: "admin_menu"}},
		}
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      callback.Message.Message.Chat.ID,
			Text:        text,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{InlineKeyboard: keyboard},
		})
		if err == nil {
			sentMessages = append(sentMessages, msg.ID)
		}
	} else {
		for _, tariff := range pagedTariffs {
			text, keyboard := buildTariffEntry(tariff, page)
			msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:      callback.Message.Message.Chat.ID,
				Text:        text,
				ParseMode:   models.ParseModeHTML,
				ReplyMarkup: keyboard,
			})
			if err == nil {
				sentMessages = append(sentMessages, msg.ID)
			}
		}
	}

	var paginationKeyboard []models.InlineKeyboardButton
	if page > 1 {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "◀️ Предыдущая", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page-1)})
	}
	paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "➕ Создать", CallbackData: "tariff_add"})
	if page < totalPages {
		paginationKeyboard = append(paginationKeyboard, models.InlineKeyboardButton{Text: "▶️ Следующая", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page+1)})
	}

	navKeyboard := [][]models.InlineKeyboardButton{paginationKeyboard, {{Text: "⬅️ Назад", CallbackData: "admin_menu"}}}
	msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      callback.Message.Message.Chat.ID,
		Text:        fmt.Sprintf("Страница %d из %d", page, totalPages),
		ReplyMarkup: models.InlineKeyboardMarkup{InlineKeyboard: navKeyboard},
	})
	if err == nil {
		sentMessages = append(sentMessages, msg.ID)
	}

	h.cache.Set(8000000+userID, sentMessages)
}

func (h *TariffHandler) clearTariffMessages(ctx context.Context, b *bot.Bot, userID, chatID int64, exceptMsgID ...int) {
	if oldMsgIDs, ok := h.cache.Get(8000000 + userID); ok {
		if ids, ok := oldMsgIDs.([]int); ok {
			for _, id := range ids {
				skip := false
				for _, except := range exceptMsgID {
					if id == except {
						skip = true
						break
					}
				}
				if skip {
					continue
				}
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    chatID,
					MessageID: id,
				})
			}
		}
		h.cache.Delete(8000000 + userID)
	}
}

func buildTariffEntry(tariff database.Tariff, page int) (string, models.InlineKeyboardMarkup) {
	var status string
	if tariff.Active {
		status = "✅ Активен"
	} else {
		status = "❌ Неактивен"
	}

	// Формируем информацию о трафике
	var trafficText string
	if tariff.TrafficLimitGB == 0 {
		trafficText = "безлимитный"
	} else {
		trafficText = fmt.Sprintf("%d ГБ", tariff.TrafficLimitGB)
	}

	text := fmt.Sprintf(
		"<b>Тариф %s</b>\n\nНазвание: %s\nСтатус: %s\nДней: %d\nТрафик: %s\nАудитория: Всем пользователям\nЦена: %d₽ / %d⭐",
		tariff.Code, tariff.Title, status, tariff.Days, trafficText, tariff.PriceRUB, tariff.PriceStars,
	)

	var toggleButtonText string
	if tariff.Active {
		toggleButtonText = "❌ Выключить"
	} else {
		toggleButtonText = "✅ Включить"
	}

	keyboard := [][]models.InlineKeyboardButton{
		{
			{Text: "✏️ Редактировать", CallbackData: fmt.Sprintf("tariff_edit_%s_p%d", tariff.Code, page)},
			{Text: "🗑 Удалить", CallbackData: fmt.Sprintf("tariff_delete_%s_p%d", tariff.Code, page)},
		},
		{
			{Text: toggleButtonText, CallbackData: fmt.Sprintf("tariff_toggle_%s_p%d", tariff.Code, page)},
		},
	}

	return text, models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

package handler

import (
	"context"
	"fmt"
	"strings"
	"time"

	"log/slog"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"

	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/utils"
)

func (h Handler) ConnectCommandHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	customer, err := h.customerRepository.FindByTelegramId(ctx, update.Message.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", err)
		return
	}
	if customer == nil {
		slog.Error("customer not exist", "telegramId", utils.MaskHalfInt64(update.Message.Chat.ID), "error", err)
		return
	}

	langCode := update.Message.From.LanguageCode

	isDisabled := true
	_, err = b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    update.Message.Chat.ID,
		Text:      h.buildConnectText(ctx, customer, langCode),
		ParseMode: models.ParseModeHTML,
		LinkPreviewOptions: &models.LinkPreviewOptions{
			IsDisabled: &isDisabled,
		},
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackStart}},
			},
		},
	})

	if err != nil {
		slog.Error("Error sending connect message", err)
	}
}

func (h Handler) ConnectCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", err)
		return
	}
	if customer == nil {
		slog.Error("customer not exist", "telegramId", utils.MaskHalfInt64(callback.Chat.ID), "error", err)
		return
	}

	langCode := update.CallbackQuery.From.LanguageCode

	isDisabled := true
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		ParseMode: models.ParseModeHTML,
		Text:      h.buildConnectText(ctx, customer, langCode),
		LinkPreviewOptions: &models.LinkPreviewOptions{
			IsDisabled: &isDisabled,
		},
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackStart}},
			},
		},
	})

	if err != nil {
		slog.Error("Error sending connect message", err)
	}
}

// SubscriptionInfoCallbackHandler обрабатывает нажатие на кнопку "Информация о подписке"
func (h Handler) SubscriptionInfoCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
	if err != nil {
		slog.Error("Error finding customer", err)
		return
	}
	if customer == nil {
		slog.Error("customer not exist", "telegramId", utils.MaskHalfInt64(callback.Chat.ID), "error", err)
		return
	}

	isDisabled := true
	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		ParseMode: models.ParseModeHTML,
		Text:      h.buildConnectText(ctx, customer, langCode),
		LinkPreviewOptions: &models.LinkPreviewOptions{
			IsDisabled: &isDisabled,
		},
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackStart}},
			},
		},
	})

	if err != nil {
		slog.Error("Error sending subscription info message", err)
	}
}

// formatMoscowTime форматирует время в московском часовом поясе с корректным смещением UTC
func formatMoscowTime(t time.Time) string {
	moscowLocation, _ := time.LoadLocation("Europe/Moscow")
	moscowTime := t.In(moscowLocation)

	// Получаем смещение от UTC для московского времени
	_, offset := moscowTime.Zone()
	hours := offset / 3600

	// Форматируем время с корректным смещением UTC
	return moscowTime.Format("02.01.2006 15:04") + fmt.Sprintf(" (UTC%+d)", hours)
}

func (h Handler) buildConnectText(ctx context.Context, customer *database.Customer, langCode string) string {
	var info strings.Builder

	tm := translation.GetInstance()

	if customer.ExpireAt != nil {
		currentTime := time.Now()

		if currentTime.Before(*customer.ExpireAt) {
			// Форматируем дату в московском времени
			formattedDate := formatMoscowTime(*customer.ExpireAt)

			// Вычисляем количество оставшихся дней
			nowDate := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())
			expireDate := time.Date(customer.ExpireAt.Year(), customer.ExpireAt.Month(), customer.ExpireAt.Day(), 0, 0, 0, 0, customer.ExpireAt.Location())
			duration := expireDate.Sub(nowDate)
			daysRemaining := int(duration.Hours() / 24)

			// Формируем текст с оставшимися днями
			var expirationText string
			if daysRemaining > 0 {
				expirationText = fmt.Sprintf("%s (осталось %d дней)", formattedDate, daysRemaining)
			} else if daysRemaining == 0 {
				expirationText = fmt.Sprintf("%s (истекает сегодня)", formattedDate)
			} else {
				expirationText = formattedDate // Если дата уже прошла, показываем только дату
			}

			info.WriteString(fmt.Sprintf("📱 <b>Моя подписка</b>\n\n"))
			info.WriteString(fmt.Sprintf("✅ <b>Статус:</b> Активна\n"))

			// Получаем информацию о тарифе из последней оплаченной покупки
			lastPurchase, err := h.purchaseRepository.FindLastPaidByCustomerID(ctx, customer.ID)
			if err == nil && lastPurchase != nil && lastPurchase.TariffCode != nil && *lastPurchase.TariffCode != "" {
				tariff, err := h.tariffRepository.GetByCode(ctx, *lastPurchase.TariffCode)
				if err == nil && tariff != nil {
					info.WriteString(fmt.Sprintf("📦 <b>Тариф:</b> %s\n", tariff.Title))
				}
			}

			info.WriteString(fmt.Sprintf("⏰ <b>Действует до:</b> %s\n\n", expirationText))
			info.WriteString("🔔 Вы получите уведомление за 3 дня до окончания подписки.")
		} else {
			noSubscriptionText := tm.GetText(langCode, "no_subscription")
			info.WriteString(fmt.Sprintf("📱 <b>Моя подписка</b>\n\n"))
			info.WriteString(fmt.Sprintf("❌ <b>Статус:</b> %s\n\n", noSubscriptionText))
			info.WriteString("💰 Для продления подписки вернитесь в главное меню и нажмите \"Купить\".")
		}
	} else {
		noSubscriptionText := tm.GetText(langCode, "no_subscription")
		info.WriteString(fmt.Sprintf("📱 <b>Моя подписка</b>\n\n"))
		info.WriteString(fmt.Sprintf("❌ <b>Статус:</b> %s\n\n", noSubscriptionText))
		info.WriteString("💰 Для активации подписки вернитесь в главное меню и нажмите \"Купить\".")
	}

	return info.String()
}

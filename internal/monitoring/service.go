package monitoring

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"remnawave-tg-shop-bot/internal/remnawave"
	"strings"
	"sync"
	"time"

	remapi "github.com/snaplyze/remnawave-api-go/v2/api"
)

// Service представляет сервис мониторинга
type Service struct {
	remnawaveClient *remnawave.Client
	config          MonitoringConfig
	nodeStatuses    map[string]*NodeStatus
	systemStatus    *SystemStatus
	mutex           sync.RWMutex
	lastUpdate      time.Time
}

// NewService создает новый сервис мониторинга
func NewService(remnawaveClient *remnawave.Client) *Service {
	return &Service{
		remnawaveClient: remnawaveClient,
		config: MonitoringConfig{
			UpdateInterval:      30 * time.Second,
			TimeoutDuration:     10 * time.Second,
			MaxRetries:          3,
			EnableWebSocket:     true,
			EnableNotifications: true,
		},
		nodeStatuses: make(map[string]*NodeStatus),
		systemStatus: &SystemStatus{
			SystemHealth: "unknown",
			LastUpdate:   time.Now(),
		},
	}
}

// GetAllNodes возвращает статус всех нод
func (s *Service) GetAllNodes(ctx context.Context) ([]NodeStatus, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	nodes := make([]NodeStatus, 0, len(s.nodeStatuses))
	for _, node := range s.nodeStatuses {
		nodes = append(nodes, *node)
	}

	return nodes, nil
}

// GetNodeByID возвращает статус конкретной ноды
func (s *Service) GetNodeByID(ctx context.Context, nodeID string) (*NodeStatus, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	node, exists := s.nodeStatuses[nodeID]
	if !exists {
		return nil, fmt.Errorf("node with ID %s not found", nodeID)
	}

	return node, nil
}

// GetSystemStatus возвращает общий статус системы
func (s *Service) GetSystemStatus(ctx context.Context) (*SystemStatus, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return s.systemStatus, nil
}

// UpdateNodeStatuses обновляет статусы всех нод
func (s *Service) UpdateNodeStatuses(ctx context.Context) error {
	slog.Info("Updating node statuses")

	// Получаем список нод из RemnaWave API
	nodes, err := s.fetchNodesFromAPI(ctx)
	if err != nil {
		slog.Error("Failed to fetch nodes from API", "error", err)
		return err
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Обновляем статусы нод
	newNodeStatuses := make(map[string]*NodeStatus)
	var totalTraffic Traffic
	var totalLatency int64
	var onlineCount, offlineCount, errorCount int

	for _, node := range nodes {
		nodeStatus := s.checkNodeStatus(ctx, &node)
		newNodeStatuses[nodeStatus.ID] = nodeStatus

		// Агрегируем статистику
		totalTraffic.Upload += nodeStatus.Traffic.Upload
		totalTraffic.Download += nodeStatus.Traffic.Download
		totalTraffic.Total += nodeStatus.Traffic.Total
		totalLatency += int64(nodeStatus.Latency)

		switch nodeStatus.Status {
		case "online":
			onlineCount++
		case "offline":
			offlineCount++
		case "error":
			errorCount++
		}
	}

	s.nodeStatuses = newNodeStatuses
	s.lastUpdate = time.Now()

	// Обновляем системный статус
	s.updateSystemStatus(len(nodes), onlineCount, offlineCount, errorCount, totalTraffic, totalLatency)

	slog.Info("Node statuses updated",
		"total_nodes", len(nodes),
		"online", onlineCount,
		"offline", offlineCount,
		"error", errorCount)

	return nil
}

// fetchNodesFromAPI получает список нод из RemnaWave API
func (s *Service) fetchNodesFromAPI(ctx context.Context) ([]remapi.GetAllNodesResponseDtoResponseItem, error) {
	// Создаем контекст с таймаутом
	timeoutCtx, cancel := context.WithTimeout(ctx, s.config.TimeoutDuration)
	defer cancel()

	// Получаем все ноды через RemnaWave API
	nodes, err := s.remnawaveClient.GetNodes(timeoutCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to get nodes: %w", err)
	}

	if nodes == nil {
		return []remapi.GetAllNodesResponseDtoResponseItem{}, nil
	}

	return *nodes, nil
}

// checkNodeStatus проверяет статус конкретной ноды
func (s *Service) checkNodeStatus(ctx context.Context, node *remapi.GetAllNodesResponseDtoResponseItem) *NodeStatus {
	nodeStatus := &NodeStatus{
		ID:       node.UUID.String(),
		Name:     node.Name,
		Address:  node.Address,
		Status:   "offline",
		LastSeen: time.Now(),
		Latency:  0,
		Traffic:  Traffic{},
		Load:     Load{},
	}

	// Определяем статус на основе данных из API
	if node.IsDisabled {
		nodeStatus.Status = "disabled"
	} else if node.IsConnecting {
		nodeStatus.Status = "connecting"
	} else if node.IsConnected && node.IsNodeOnline && node.IsXrayRunning {
		nodeStatus.Status = "online"

		// Проверяем доступность ноды для получения latency
		port := 443 // Порт по умолчанию
		if !node.Port.IsNull() {
			if p, ok := node.Port.Get(); ok {
				port = p
			}
		}

		if latency, err := s.pingNode(ctx, node.Address, port); err == nil {
			nodeStatus.Latency = latency
		}
	} else {
		nodeStatus.Status = "offline"
	}

	// Устанавливаем время последнего изменения статуса
	if !node.LastStatusChange.IsNull() {
		if lastChange, ok := node.LastStatusChange.Get(); ok {
			nodeStatus.LastSeen = lastChange
		}
	}

	// Получаем дополнительную информацию о ноде
	s.enrichNodeStatus(ctx, nodeStatus, node)

	return nodeStatus
}

// pingNode проверяет доступность ноды
func (s *Service) pingNode(ctx context.Context, address string, port int) (int, error) {
	start := time.Now()

	// Создаем контекст с таймаутом для ping
	pingCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Пытаемся подключиться к ноде
	dialer := &net.Dialer{}
	conn, err := dialer.DialContext(pingCtx, "tcp", fmt.Sprintf("%s:%d", address, port))
	if err != nil {
		return 0, err
	}
	defer conn.Close()

	latency := int(time.Since(start).Milliseconds())
	return latency, nil
}

// enrichNodeStatus обогащает информацию о ноде дополнительными данными
func (s *Service) enrichNodeStatus(ctx context.Context, nodeStatus *NodeStatus, node *remapi.GetAllNodesResponseDtoResponseItem) {
	// Получаем статистику трафика (если доступно)
	if traffic := s.getNodeTraffic(ctx, node); traffic != nil {
		nodeStatus.Traffic = *traffic
	}

	// Вычисляем uptime
	nodeStatus.Uptime = s.calculateUptime(nodeStatus.LastSeen)

	// Устанавливаем версию и локацию (если доступно)
	if !node.NodeVersion.IsNull() {
		if version, ok := node.NodeVersion.Get(); ok {
			nodeStatus.Version = version
		}
	}

	// Устанавливаем локацию на основе кода страны
	nodeStatus.Location = s.getLocationFromCountryCode(node.CountryCode)

	// Получаем информацию о нагрузке
	if !node.UsersOnline.IsNull() {
		if users, ok := node.UsersOnline.Get(); ok {
			nodeStatus.Load.Users = users
		}
	}

	// Устанавливаем uptime на основе XrayUptime
	nodeStatus.Uptime = node.XrayUptime
}

// getNodeTraffic получает статистику трафика ноды
func (s *Service) getNodeTraffic(ctx context.Context, node *remapi.GetAllNodesResponseDtoResponseItem) *Traffic {
	traffic := &Traffic{}

	// Получаем данные о трафике из ноды
	if !node.TrafficUsedBytes.IsNull() {
		if used, ok := node.TrafficUsedBytes.Get(); ok {
			traffic.Total = int64(used)
		}
	}

	// Пока upload и download не разделены в API, используем общий трафик
	traffic.Upload = traffic.Total / 2
	traffic.Download = traffic.Total / 2

	return traffic
}

// calculateUptime вычисляет время работы ноды
func (s *Service) calculateUptime(lastSeen time.Time) string {
	duration := time.Since(lastSeen)

	days := int(duration.Hours()) / 24
	hours := int(duration.Hours()) % 24
	minutes := int(duration.Minutes()) % 60

	if days > 0 {
		return fmt.Sprintf("%dd %dh %dm", days, hours, minutes)
	} else if hours > 0 {
		return fmt.Sprintf("%dh %dm", hours, minutes)
	} else {
		return fmt.Sprintf("%dm", minutes)
	}
}

// getLocationFromCountryCode получает название локации по коду страны
func (s *Service) getLocationFromCountryCode(countryCode string) string {
	countryMap := map[string]string{
		"US": "United States",
		"CA": "Canada",
		"GB": "United Kingdom",
		"DE": "Germany",
		"FR": "France",
		"NL": "Netherlands",
		"SG": "Singapore",
		"JP": "Japan",
		"AU": "Australia",
		"RU": "Russia",
		"UA": "Ukraine",
		"PL": "Poland",
		"IT": "Italy",
		"ES": "Spain",
		"BR": "Brazil",
		"IN": "India",
		"CN": "China",
		"KR": "South Korea",
		"SE": "Sweden",
		"NO": "Norway",
		"FI": "Finland",
		"DK": "Denmark",
		"CH": "Switzerland",
		"AT": "Austria",
		"BE": "Belgium",
		"CZ": "Czech Republic",
		"HU": "Hungary",
		"RO": "Romania",
		"BG": "Bulgaria",
		"HR": "Croatia",
		"SI": "Slovenia",
		"SK": "Slovakia",
		"LT": "Lithuania",
		"LV": "Latvia",
		"EE": "Estonia",
	}

	if location, exists := countryMap[strings.ToUpper(countryCode)]; exists {
		return location
	}
	return countryCode // Возвращаем код страны, если не найдено соответствие
}

// updateSystemStatus обновляет общий статус системы
func (s *Service) updateSystemStatus(total, online, offline, error int, traffic Traffic, totalLatency int64) {
	s.systemStatus.TotalNodes = total
	s.systemStatus.OnlineNodes = online
	s.systemStatus.OfflineNodes = offline
	s.systemStatus.ErrorNodes = error
	s.systemStatus.LastUpdate = time.Now()
	s.systemStatus.TotalTraffic = traffic

	if total > 0 {
		s.systemStatus.AverageLatency = int(totalLatency / int64(total))
	}

	// Определяем общее состояние системы
	if error > 0 || offline > online {
		s.systemStatus.SystemHealth = "critical"
	} else if offline > 0 {
		s.systemStatus.SystemHealth = "degraded"
	} else {
		s.systemStatus.SystemHealth = "healthy"
	}
}

// PerformHealthCheck выполняет проверку здоровья системы
func (s *Service) PerformHealthCheck(ctx context.Context) ([]HealthCheckResult, error) {
	results := make([]HealthCheckResult, 0)

	// Проверка RemnaWave API
	start := time.Now()
	err := s.remnawaveClient.Ping(ctx)
	duration := time.Since(start).Milliseconds()

	apiResult := HealthCheckResult{
		Service:   "remnawave_api",
		Timestamp: time.Now(),
		Duration:  duration,
	}

	if err != nil {
		apiResult.Status = "error"
		apiResult.Message = err.Error()
	} else {
		apiResult.Status = "ok"
		apiResult.Message = "API is responding"
	}

	results = append(results, apiResult)

	// Проверка общего состояния нод
	s.mutex.RLock()
	systemStatus := *s.systemStatus
	s.mutex.RUnlock()

	nodesResult := HealthCheckResult{
		Service:   "nodes_status",
		Timestamp: time.Now(),
		Duration:  0,
	}

	if systemStatus.SystemHealth == "healthy" {
		nodesResult.Status = "ok"
		nodesResult.Message = fmt.Sprintf("%d/%d nodes online", systemStatus.OnlineNodes, systemStatus.TotalNodes)
	} else if systemStatus.SystemHealth == "degraded" {
		nodesResult.Status = "warning"
		nodesResult.Message = fmt.Sprintf("%d/%d nodes online, %d offline",
			systemStatus.OnlineNodes, systemStatus.TotalNodes, systemStatus.OfflineNodes)
	} else {
		nodesResult.Status = "error"
		nodesResult.Message = fmt.Sprintf("Critical: %d/%d nodes online, %d offline, %d errors",
			systemStatus.OnlineNodes, systemStatus.TotalNodes, systemStatus.OfflineNodes, systemStatus.ErrorNodes)
	}

	results = append(results, nodesResult)

	return results, nil
}

// StartPeriodicUpdates запускает периодическое обновление статусов
func (s *Service) StartPeriodicUpdates(ctx context.Context) {
	ticker := time.NewTicker(s.config.UpdateInterval)
	defer ticker.Stop()

	// Первоначальное обновление
	if err := s.UpdateNodeStatuses(ctx); err != nil {
		slog.Error("Initial node status update failed", "error", err)
	}

	for {
		select {
		case <-ctx.Done():
			slog.Info("Stopping periodic node status updates")
			return
		case <-ticker.C:
			if err := s.UpdateNodeStatuses(ctx); err != nil {
				slog.Error("Periodic node status update failed", "error", err)
			}
		}
	}
}

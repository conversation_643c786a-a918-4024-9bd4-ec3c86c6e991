package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// Handler представляет HTTP обработчик для мониторинга
type Handler struct {
	service  *Service
	upgrader websocket.Upgrader
}

// NewHandler создает новый обработчик мониторинга
func NewHandler(service *Service) *Handler {
	return &Handler{
		service: service,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// В продакшене здесь должна быть более строгая проверка
				return true
			},
		},
	}
}

// ServeHTTP обрабатывает HTTP запросы для мониторинга
func (h *Handler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Устанавливаем CORS заголовки
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Маршрутизация запросов
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/monitoring")
	
	switch {
	case path == "/nodes":
		h.handleGetAllNodes(w, r)
	case path == "/nodes/status":
		h.handleGetNodesStatus(w, r)
	case strings.HasPrefix(path, "/nodes/"):
		nodeID := strings.TrimPrefix(path, "/nodes/")
		h.handleGetNodeByID(w, r, nodeID)
	case path == "/health":
		h.handleHealthCheck(w, r)
	case path == "/system":
		h.handleGetSystemStatus(w, r)
	default:
		h.sendErrorResponse(w, "Not found", http.StatusNotFound)
	}
}

// handleGetAllNodes обрабатывает запрос получения всех нод
func (h *Handler) handleGetAllNodes(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	nodes, err := h.service.GetAllNodes(ctx)
	if err != nil {
		slog.Error("Failed to get all nodes", "error", err)
		h.sendErrorResponse(w, "Failed to get nodes", http.StatusInternalServerError)
		return
	}

	response := MonitoringResponse{
		Success: true,
		Data:    nodes,
		Meta: Meta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}

	h.sendJSONResponse(w, response, http.StatusOK)
}

// handleGetNodesStatus обрабатывает запрос получения статуса всех нод
func (h *Handler) handleGetNodesStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	// Обновляем статусы нод
	if err := h.service.UpdateNodeStatuses(ctx); err != nil {
		slog.Error("Failed to update node statuses", "error", err)
		h.sendErrorResponse(w, "Failed to update node statuses", http.StatusInternalServerError)
		return
	}

	nodes, err := h.service.GetAllNodes(ctx)
	if err != nil {
		slog.Error("Failed to get all nodes", "error", err)
		h.sendErrorResponse(w, "Failed to get nodes", http.StatusInternalServerError)
		return
	}

	response := MonitoringResponse{
		Success: true,
		Data:    nodes,
		Meta: Meta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}

	h.sendJSONResponse(w, response, http.StatusOK)
}

// handleGetNodeByID обрабатывает запрос получения конкретной ноды
func (h *Handler) handleGetNodeByID(w http.ResponseWriter, r *http.Request, nodeID string) {
	ctx := r.Context()
	
	// Проверяем валидность UUID
	if _, err := uuid.Parse(nodeID); err != nil {
		h.sendErrorResponse(w, "Invalid node ID format", http.StatusBadRequest)
		return
	}

	node, err := h.service.GetNodeByID(ctx, nodeID)
	if err != nil {
		slog.Error("Failed to get node by ID", "nodeID", nodeID, "error", err)
		h.sendErrorResponse(w, "Node not found", http.StatusNotFound)
		return
	}

	response := MonitoringResponse{
		Success: true,
		Data:    node,
		Meta: Meta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}

	h.sendJSONResponse(w, response, http.StatusOK)
}

// handleGetSystemStatus обрабатывает запрос получения общего статуса системы
func (h *Handler) handleGetSystemStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	systemStatus, err := h.service.GetSystemStatus(ctx)
	if err != nil {
		slog.Error("Failed to get system status", "error", err)
		h.sendErrorResponse(w, "Failed to get system status", http.StatusInternalServerError)
		return
	}

	response := MonitoringResponse{
		Success: true,
		Data:    systemStatus,
		Meta: Meta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}

	h.sendJSONResponse(w, response, http.StatusOK)
}

// handleHealthCheck обрабатывает запрос проверки здоровья
func (h *Handler) handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	
	healthResults, err := h.service.PerformHealthCheck(ctx)
	if err != nil {
		slog.Error("Failed to perform health check", "error", err)
		h.sendErrorResponse(w, "Failed to perform health check", http.StatusInternalServerError)
		return
	}

	response := MonitoringResponse{
		Success: true,
		Data:    healthResults,
		Meta: Meta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}

	h.sendJSONResponse(w, response, http.StatusOK)
}

// WebSocketHandler обрабатывает WebSocket соединения
func (h *Handler) WebSocketHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		conn, err := h.upgrader.Upgrade(w, r, nil)
		if err != nil {
			slog.Error("Failed to upgrade to WebSocket", "error", err)
			return
		}
		defer conn.Close()

		slog.Info("WebSocket connection established", "remote_addr", r.RemoteAddr)

		// Создаем контекст для WebSocket соединения
		ctx, cancel := context.WithCancel(r.Context())
		defer cancel()

		// Отправляем периодические обновления
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		// Отправляем начальные данные
		if err := h.sendWebSocketUpdate(conn, ctx); err != nil {
			slog.Error("Failed to send initial WebSocket update", "error", err)
			return
		}

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if err := h.sendWebSocketUpdate(conn, ctx); err != nil {
					slog.Error("Failed to send WebSocket update", "error", err)
					return
				}
			}
		}
	}
}

// sendWebSocketUpdate отправляет обновление через WebSocket
func (h *Handler) sendWebSocketUpdate(conn *websocket.Conn, ctx context.Context) error {
	// Обновляем статусы нод
	if err := h.service.UpdateNodeStatuses(ctx); err != nil {
		return fmt.Errorf("failed to update node statuses: %w", err)
	}

	// Получаем текущие статусы
	nodes, err := h.service.GetAllNodes(ctx)
	if err != nil {
		return fmt.Errorf("failed to get nodes: %w", err)
	}

	systemStatus, err := h.service.GetSystemStatus(ctx)
	if err != nil {
		return fmt.Errorf("failed to get system status: %w", err)
	}

	// Отправляем обновление статуса системы
	systemMessage := WebSocketMessage{
		Type: "system_update",
		Data: systemStatus,
		Meta: Meta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}

	if err := conn.WriteJSON(systemMessage); err != nil {
		return fmt.Errorf("failed to send system update: %w", err)
	}

	// Отправляем обновления нод
	for _, node := range nodes {
		nodeMessage := WebSocketMessage{
			Type: "node_update",
			Data: NodeUpdateMessage{
				NodeID: node.ID,
				Status: node,
			},
			Meta: Meta{
				Timestamp: time.Now(),
				Version:   "1.0",
			},
		}

		if err := conn.WriteJSON(nodeMessage); err != nil {
			return fmt.Errorf("failed to send node update: %w", err)
		}
	}

	return nil
}

// sendJSONResponse отправляет JSON ответ
func (h *Handler) sendJSONResponse(w http.ResponseWriter, data interface{}, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	
	if err := json.NewEncoder(w).Encode(data); err != nil {
		slog.Error("Failed to encode JSON response", "error", err)
	}
}

// sendErrorResponse отправляет ответ с ошибкой
func (h *Handler) sendErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	response := MonitoringResponse{
		Success: false,
		Error:   message,
		Meta: Meta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}

	h.sendJSONResponse(w, response, statusCode)
}

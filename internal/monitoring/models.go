package monitoring

import (
	"time"
)

// NodeStatus представляет статус ноды RemnaWave
type NodeStatus struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Address     string    `json:"address"`
	Status      string    `json:"status"`      // online, offline, connecting, error
	LastSeen    time.Time `json:"last_seen"`
	Latency     int       `json:"latency_ms"`
	Traffic     Traffic   `json:"traffic"`
	Uptime      string    `json:"uptime"`
	Version     string    `json:"version,omitempty"`
	Location    string    `json:"location,omitempty"`
	Load        Load      `json:"load"`
}

// Traffic представляет информацию о трафике ноды
type Traffic struct {
	Upload   int64 `json:"upload_bytes"`
	Download int64 `json:"download_bytes"`
	Total    int64 `json:"total_bytes"`
}

// Load представляет информацию о нагрузке ноды
type Load struct {
	CPU    float64 `json:"cpu_percent"`
	Memory float64 `json:"memory_percent"`
	Users  int     `json:"active_users"`
}

// SystemStatus представляет общий статус системы
type SystemStatus struct {
	TotalNodes    int       `json:"total_nodes"`
	OnlineNodes   int       `json:"online_nodes"`
	OfflineNodes  int       `json:"offline_nodes"`
	ErrorNodes    int       `json:"error_nodes"`
	LastUpdate    time.Time `json:"last_update"`
	SystemHealth  string    `json:"system_health"` // healthy, degraded, critical
	TotalTraffic  Traffic   `json:"total_traffic"`
	AverageLatency int      `json:"average_latency_ms"`
}

// MonitoringResponse представляет ответ API мониторинга
type MonitoringResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Meta    Meta        `json:"meta"`
}

// Meta содержит метаинформацию о запросе
type Meta struct {
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
	RequestID string    `json:"request_id,omitempty"`
}

// WebSocketMessage представляет сообщение WebSocket
type WebSocketMessage struct {
	Type string      `json:"type"` // node_update, system_update, error
	Data interface{} `json:"data"`
	Meta Meta        `json:"meta"`
}

// NodeUpdateMessage представляет обновление статуса ноды через WebSocket
type NodeUpdateMessage struct {
	NodeID string     `json:"node_id"`
	Status NodeStatus `json:"status"`
}

// HealthCheckResult представляет результат проверки здоровья
type HealthCheckResult struct {
	Service   string    `json:"service"`
	Status    string    `json:"status"` // ok, warning, error
	Message   string    `json:"message,omitempty"`
	Timestamp time.Time `json:"timestamp"`
	Duration  int64     `json:"duration_ms"`
}

// MonitoringConfig представляет конфигурацию мониторинга
type MonitoringConfig struct {
	UpdateInterval    time.Duration `json:"update_interval"`
	TimeoutDuration   time.Duration `json:"timeout_duration"`
	MaxRetries        int           `json:"max_retries"`
	EnableWebSocket   bool          `json:"enable_websocket"`
	EnableNotifications bool        `json:"enable_notifications"`
}

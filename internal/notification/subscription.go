package notification

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/handler"
	"remnawave-tg-shop-bot/internal/translation"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

type SubscriptionService struct {
	customerRepository *database.CustomerRepository
	purchaseRepository *database.PurchaseRepository
	tariffRepository   *database.TariffRepository
	telegramBot        *bot.Bot
	tm                 *translation.Manager
}

func NewSubscriptionService(customerRepository *database.CustomerRepository, purchaseRepository *database.PurchaseRepository, tariffRepository *database.TariffRepository, telegramBot *bot.Bot, tm *translation.Manager) *SubscriptionService {
	return &SubscriptionService{
		customerRepository: customerRepository,
		purchaseRepository: purchaseRepository,
		tariffRepository:   tariffRepository,
		telegramBot:        telegramBot,
		tm:                 tm,
	}
}

func (s *SubscriptionService) SendSubscriptionNotifications(ctx context.Context) error {
	// Отправляем уведомления за 3, 2 и 1 день до истечения подписки
	notificationDays := []int{3, 2, 1}

	for _, days := range notificationDays {
		customers, err := s.getCustomersWithExpiringSubscriptions(ctx, days)
		if err != nil {
			slog.Error("failed to get customers with expiring subscriptions", "days", days, "error", err)
			continue
		}

		if customers == nil || len(*customers) == 0 {
			slog.Info("no customers with expiring subscriptions found", "days", days)
			continue
		}

		slog.Info("found customers with expiring subscriptions", "days", days, "count", len(*customers))

		for _, customer := range *customers {
			// Проверяем, не отправляли ли уже уведомление за этот период
			if s.shouldSendNotification(customer, days) {
				if err := s.sendNotification(ctx, customer, days); err != nil {
					slog.Error("failed to send notification", "customer_id", customer.ID, "days", days, "error", err)
					continue
				}

				// Обновляем информацию об отправленном уведомлении
				if err := s.markNotificationSent(ctx, customer.ID, days); err != nil {
					slog.Error("failed to mark notification as sent", "customer_id", customer.ID, "days", days, "error", err)
				}

				slog.Info("notification sent successfully", "customer_id", customer.ID, "days", days)
			} else {
				slog.Debug("notification already sent", "customer_id", customer.ID, "days", days)
			}
		}
	}

	return nil
}

func (s *SubscriptionService) getCustomersWithExpiringSubscriptions(ctx context.Context, daysUntilExpiration int) (*[]database.Customer, error) {
	now := time.Now()
	// Вычисляем точную дату для поиска пользователей, у которых подписка истекает через указанное количество дней
	targetDate := now.AddDate(0, 0, daysUntilExpiration)

	// Ищем пользователей, у которых подписка истекает в указанный день (с точностью до дня)
	startOfDay := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())
	endOfDay := startOfDay.AddDate(0, 0, 1).Add(-time.Nanosecond)

	dbCustomers, err := s.customerRepository.FindByExpirationRange(ctx, startOfDay, endOfDay)
	if err != nil {
		return nil, err
	}

	return dbCustomers, nil
}

func (s *SubscriptionService) getDaysUntilExpiration(now time.Time, expireAt time.Time) int {
	nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	expireDate := time.Date(expireAt.Year(), expireAt.Month(), expireAt.Day(), 0, 0, 0, 0, expireAt.Location())

	duration := expireDate.Sub(nowDate)
	return int(duration.Hours() / 24)
}

func (s *SubscriptionService) sendNotification(ctx context.Context, customer database.Customer, daysUntilExpiration int) error {
	expireDate := customer.ExpireAt.Format("02.01.2006")

	// Получаем информацию о тарифе пользователя
	tariffName := "Стандартный"
	lastPurchase, err := s.purchaseRepository.FindLastPaidByCustomerID(ctx, customer.ID)
	if err == nil && lastPurchase != nil && lastPurchase.TariffCode != nil && *lastPurchase.TariffCode != "" {
		tariff, err := s.tariffRepository.GetByCode(ctx, *lastPurchase.TariffCode)
		if err == nil && tariff != nil {
			tariffName = tariff.Title
		}
	}

	messageText := fmt.Sprintf(
		s.tm.GetText(customer.Language, "subscription_expiring"),
		tariffName,
		daysUntilExpiration,
		expireDate,
	)

	_, err = s.telegramBot.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    customer.TelegramID,
		Text:      messageText,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{
					{
						Text:         s.tm.GetText(customer.Language, "renew_subscription_button"),
						CallbackData: handler.CallbackBuy,
					},
				},
			},
		},
	})

	return err
}

// shouldSendNotification проверяет, нужно ли отправлять уведомление пользователю
func (s *SubscriptionService) shouldSendNotification(customer database.Customer, days int) bool {
	// Если поле notification_days_sent пустое, значит уведомления еще не отправлялись
	if customer.NotificationDaysSent == nil || *customer.NotificationDaysSent == "" {
		return true
	}

	// Парсим JSON массив отправленных дней
	var sentDays []int
	if err := json.Unmarshal([]byte(*customer.NotificationDaysSent), &sentDays); err != nil {
		// Если не удалось распарсить, считаем что уведомления не отправлялись
		return true
	}

	// Проверяем, есть ли уже отправленное уведомление за этот период
	for _, sentDay := range sentDays {
		if sentDay == days {
			return false
		}
	}

	return true
}

// markNotificationSent отмечает, что уведомление за указанное количество дней было отправлено
func (s *SubscriptionService) markNotificationSent(ctx context.Context, customerID int64, days int) error {
	// Получаем текущего пользователя
	customer, err := s.customerRepository.FindById(ctx, customerID)
	if err != nil {
		return fmt.Errorf("failed to find customer: %w", err)
	}
	if customer == nil {
		return fmt.Errorf("customer not found")
	}

	// Парсим существующий массив дней
	var sentDays []int
	if customer.NotificationDaysSent != nil && *customer.NotificationDaysSent != "" {
		if err := json.Unmarshal([]byte(*customer.NotificationDaysSent), &sentDays); err != nil {
			// Если не удалось распарсить, начинаем с пустого массива
			sentDays = []int{}
		}
	}

	// Добавляем новый день, если его еще нет
	found := false
	for _, sentDay := range sentDays {
		if sentDay == days {
			found = true
			break
		}
	}
	if !found {
		sentDays = append(sentDays, days)
	}

	// Сериализуем обратно в JSON
	sentDaysJSON, err := json.Marshal(sentDays)
	if err != nil {
		return fmt.Errorf("failed to marshal sent days: %w", err)
	}

	// Обновляем поля в базе данных
	now := time.Now()
	updates := map[string]interface{}{
		"last_notification_sent_at": now,
		"notification_days_sent":    string(sentDaysJSON),
	}

	return s.customerRepository.UpdateFields(ctx, customerID, updates)
}

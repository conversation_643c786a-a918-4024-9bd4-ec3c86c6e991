# RemnaWave Monitoring API Documentation

## Обзор

API мониторинга RemnaWave предоставляет REST эндпоинты и WebSocket соединения для получения информации о состоянии VPN серверов в реальном времени.

**Base URL**: `https://status.unveilvpn.com/api/v1/monitoring`

## Аутентификация

В текущей версии API не требует аутентификации для публичных эндпоинтов мониторинга.

## Структуры данных

### NodeStatus

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440001",
  "name": "US-East-1",
  "address": "us-east.example.com",
  "status": "online",
  "last_seen": "2025-01-09T12:00:00Z",
  "latency_ms": 45,
  "traffic": {
    "upload_bytes": **********,
    "download_bytes": **********,
    "total_bytes": **********
  },
  "uptime": "2d 14h 30m",
  "version": "1.8.4",
  "location": "United States",
  "load": {
    "cpu_percent": 25.5,
    "memory_percent": 60.2,
    "active_users": 15
  }
}
```

### SystemStatus

```json
{
  "total_nodes": 3,
  "online_nodes": 2,
  "offline_nodes": 1,
  "error_nodes": 0,
  "last_update": "2025-01-09T12:00:00Z",
  "system_health": "degraded",
  "average_latency_ms": 38,
  "total_traffic": {
    "upload_bytes": 10737418240,
    "download_bytes": 16106127360,
    "total_bytes": 26843545600
  }
}
```

### MonitoringResponse

```json
{
  "success": true,
  "data": {},
  "error": "",
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0",
    "request_id": "req-123456"
  }
}
```

## REST API Эндпоинты

### GET /nodes

Получить список всех нод с базовой информацией.

**Ответ:**
```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440001",
      "name": "US-East-1",
      "address": "us-east.example.com",
      "status": "online",
      "location": "United States"
    }
  ],
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

### GET /nodes/status

Получить детальный статус всех нод с обновлением данных.

**Ответ:**
```json
{
  "success": true,
  "data": [
    // Массив объектов NodeStatus
  ],
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

### GET /nodes/{id}

Получить детальную информацию о конкретной ноде.

**Параметры:**
- `id` (string) - UUID ноды

**Ответ:**
```json
{
  "success": true,
  "data": {
    // Объект NodeStatus
  },
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

**Ошибки:**
- `404` - Нода не найдена
- `400` - Неверный формат UUID

### GET /system

Получить общий статус системы.

**Ответ:**
```json
{
  "success": true,
  "data": {
    // Объект SystemStatus
  },
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

### GET /health

Проверка здоровья системы мониторинга.

**Ответ:**
```json
{
  "success": true,
  "data": [
    {
      "service": "remnawave_api",
      "status": "ok",
      "message": "API is responding",
      "timestamp": "2025-01-09T12:00:00Z",
      "duration_ms": 150
    },
    {
      "service": "nodes_status",
      "status": "ok",
      "message": "2/3 nodes online",
      "timestamp": "2025-01-09T12:00:00Z",
      "duration_ms": 0
    }
  ],
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

## WebSocket API

### Подключение

**URL**: `wss://status.unveilvpn.com/ws/monitoring`

### Типы сообщений

#### system_update

Обновление общего статуса системы.

```json
{
  "type": "system_update",
  "data": {
    // Объект SystemStatus
  },
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

#### node_update

Обновление статуса конкретной ноды.

```json
{
  "type": "node_update",
  "data": {
    "node_id": "550e8400-e29b-41d4-a716-446655440001",
    "status": {
      // Объект NodeStatus
    }
  },
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

#### error

Сообщение об ошибке.

```json
{
  "type": "error",
  "data": {
    "message": "Connection to RemnaWave API failed",
    "code": "API_CONNECTION_ERROR"
  },
  "meta": {
    "timestamp": "2025-01-09T12:00:00Z",
    "version": "1.0"
  }
}
```

## Статусы нод

| Статус | Описание |
|--------|----------|
| `online` | Нода работает нормально |
| `offline` | Нода недоступна |
| `connecting` | Нода в процессе подключения |
| `error` | Ошибка в работе ноды |
| `disabled` | Нода отключена администратором |

## Статусы системы

| Статус | Описание |
|--------|----------|
| `healthy` | Все ноды работают нормально |
| `degraded` | Некоторые ноды недоступны |
| `critical` | Большинство нод недоступно или есть критические ошибки |

## Коды ошибок

| Код | Описание |
|-----|----------|
| `200` | Успешный запрос |
| `400` | Неверный запрос |
| `404` | Ресурс не найден |
| `500` | Внутренняя ошибка сервера |
| `503` | Сервис временно недоступен |

## Примеры использования

### JavaScript (Fetch API)

```javascript
// Получить статус всех нод
async function getNodesStatus() {
  try {
    const response = await fetch('https://status.unveilvpn.com/api/v1/monitoring/nodes/status');
    const data = await response.json();
    
    if (data.success) {
      console.log('Nodes:', data.data);
    } else {
      console.error('Error:', data.error);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}

// WebSocket подключение
const ws = new WebSocket('wss://status.unveilvpn.com/ws/monitoring');

ws.onopen = function() {
  console.log('WebSocket connected');
};

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  console.log('Received:', message);
  
  switch (message.type) {
    case 'system_update':
      updateSystemStatus(message.data);
      break;
    case 'node_update':
      updateNodeStatus(message.data.node_id, message.data.status);
      break;
    case 'error':
      console.error('WebSocket error:', message.data);
      break;
  }
};
```

### cURL

```bash
# Получить статус всех нод
curl -X GET "https://status.unveilvpn.com/api/v1/monitoring/nodes/status" \
  -H "Accept: application/json"

# Получить статус конкретной ноды
curl -X GET "https://status.unveilvpn.com/api/v1/monitoring/nodes/550e8400-e29b-41d4-a716-446655440001" \
  -H "Accept: application/json"

# Проверка здоровья
curl -X GET "https://status.unveilvpn.com/api/v1/monitoring/health" \
  -H "Accept: application/json"
```

### Python

```python
import requests
import websocket
import json

# REST API
def get_nodes_status():
    response = requests.get('https://status.unveilvpn.com/api/v1/monitoring/nodes/status')
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            return data['data']
    return None

# WebSocket
def on_message(ws, message):
    data = json.loads(message)
    print(f"Received: {data}")

def on_error(ws, error):
    print(f"WebSocket error: {error}")

def on_close(ws, close_status_code, close_msg):
    print("WebSocket closed")

def on_open(ws):
    print("WebSocket connected")

# Подключение к WebSocket
ws = websocket.WebSocketApp("wss://status.unveilvpn.com/ws/monitoring",
                          on_open=on_open,
                          on_message=on_message,
                          on_error=on_error,
                          on_close=on_close)

ws.run_forever()
```

## Rate Limiting

API имеет ограничения на количество запросов:

- **API эндпоинты**: 10 запросов/сек с burst до 20
- **Веб-интерфейс**: 5 запросов/сек с burst до 10
- **WebSocket**: Без ограничений на подключения

## CORS

API поддерживает CORS для всех доменов (`Access-Control-Allow-Origin: *`).

## Версионирование

Текущая версия API: `v1`

Версия указывается в URL (`/api/v1/monitoring/`) и в поле `meta.version` ответов.

## Поддержка

При возникновении проблем с API:

1. Проверьте статус сервиса: `GET /health`
2. Убедитесь в корректности URL и параметров
3. Проверьте сетевое подключение
4. Обратитесь в техническую поддержку

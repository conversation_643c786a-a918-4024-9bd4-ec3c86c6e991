#!/bin/bash

# Скрипт для настройки доступа к приватным репозиториям GitHub
# Usage: ./scripts/setup-github-access.sh

set -e

# Цвета для вывода
COLOR_RESET="\033[0m"
COLOR_GREEN="\033[1;32m"
COLOR_YELLOW="\033[1;33m"
COLOR_RED="\033[1;31m"
COLOR_BLUE="\033[1;34m"

# Функции для логирования
log_info() {
    echo -e "${COLOR_BLUE}[INFO]${COLOR_RESET} $1"
}

log_success() {
    echo -e "${COLOR_GREEN}[SUCCESS]${COLOR_RESET} $1"
}

log_warning() {
    echo -e "${COLOR_YELLOW}[WARNING]${COLOR_RESET} $1"
}

log_error() {
    echo -e "${COLOR_RED}[ERROR]${COLOR_RESET} $1"
}

# Проверка root прав
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "Скрипт запущен с правами root. Рекомендуется запускать от обычного пользователя."
        read -p "Продолжить? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Проверка наличия .env файла
check_env_file() {
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.sample" ]]; then
            log_info "Создание .env файла из .env.sample..."
            cp .env.sample .env
            log_success ".env файл создан"
        else
            log_error ".env.sample файл не найден"
            exit 1
        fi
    fi
}

# Получение GitHub токена от пользователя
get_github_token() {
    echo
    log_info "Настройка доступа к приватному репозиторию GitHub"
    echo
    echo "Для доступа к приватному репозиторию github.com/snaplyze/remnawave-api-go"
    echo "необходимо создать Personal Access Token в GitHub."
    echo
    echo "Инструкции:"
    echo "1. Перейдите по ссылке: https://github.com/settings/tokens"
    echo "2. Нажмите 'Generate new token' -> 'Generate new token (classic)'"
    echo "3. Укажите название токена (например: 'RemnaWave Bot Access')"
    echo "4. Выберите срок действия (рекомендуется 'No expiration' для продакшн)"
    echo "5. Отметьте права доступа: 'repo' (Full control of private repositories)"
    echo "6. Нажмите 'Generate token'"
    echo "7. Скопируйте созданный токен"
    echo
    
    read -p "Нажмите Enter, когда будете готовы ввести токен..."
    echo
    
    while true; do
        read -s -p "Введите GitHub Personal Access Token: " GITHUB_TOKEN
        echo
        
        if [[ -z "$GITHUB_TOKEN" ]]; then
            log_error "Токен не может быть пустым"
            continue
        fi
        
        # Проверка формата токена (GitHub classic tokens начинаются с ghp_)
        if [[ ! "$GITHUB_TOKEN" =~ ^ghp_[a-zA-Z0-9]{36}$ ]] && [[ ! "$GITHUB_TOKEN" =~ ^github_pat_[a-zA-Z0-9_]{82}$ ]]; then
            log_warning "Токен не соответствует стандартному формату GitHub"
            read -p "Продолжить с этим токеном? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                continue
            fi
        fi
        
        break
    done
}

# Проверка токена
validate_github_token() {
    log_info "Проверка GitHub токена..."
    
    # Проверяем доступ к API GitHub
    if curl -s -H "Authorization: token $GITHUB_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/user" > /dev/null; then
        log_success "GitHub токен валиден"
    else
        log_error "GitHub токен недействителен или нет доступа к API"
        return 1
    fi
    
    # Проверяем доступ к конкретному репозиторию
    if curl -s -H "Authorization: token $GITHUB_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/snaplyze/remnawave-api-go" > /dev/null; then
        log_success "Доступ к репозиторию snaplyze/remnawave-api-go подтвержден"
    else
        log_warning "Не удалось получить доступ к репозиторию snaplyze/remnawave-api-go"
        log_warning "Убедитесь, что токен имеет права 'repo' и доступ к этому репозиторию"
        read -p "Продолжить? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi
}

# Обновление .env файла
update_env_file() {
    log_info "Обновление .env файла..."
    
    # Создаем резервную копию
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    
    # Обновляем или добавляем GITHUB_TOKEN
    if grep -q "^GITHUB_TOKEN=" .env; then
        # Обновляем существующую строку
        sed -i "s/^GITHUB_TOKEN=.*/GITHUB_TOKEN=$GITHUB_TOKEN/" .env
        log_success "GitHub токен обновлен в .env файле"
    else
        # Добавляем новую строку
        echo "" >> .env
        echo "# GitHub Access Token for private repositories" >> .env
        echo "GITHUB_TOKEN=$GITHUB_TOKEN" >> .env
        log_success "GitHub токен добавлен в .env файл"
    fi
}

# Тестирование сборки
test_build() {
    log_info "Тестирование сборки Docker образа..."
    
    if docker-compose build --no-cache bot; then
        log_success "Docker образ успешно собран"
    else
        log_error "Ошибка сборки Docker образа"
        log_error "Проверьте логи выше для диагностики проблемы"
        return 1
    fi
}

# Очистка Git credentials (для безопасности)
cleanup_git_credentials() {
    log_info "Очистка временных Git credentials..."
    
    # Очищаем глобальные настройки git в контейнере (они не сохраняются)
    # Это больше информационное сообщение
    log_success "Git credentials будут очищены после сборки контейнера"
}

# Главная функция
main() {
    echo "=================================================="
    echo "  Настройка доступа к приватным репозиториям GitHub"
    echo "=================================================="
    echo
    
    check_root
    check_env_file
    get_github_token
    
    if validate_github_token; then
        update_env_file
        
        echo
        log_info "Тестирование доступа к приватному репозиторию..."
        
        if test_build; then
            cleanup_git_credentials
            
            echo
            echo "=================================================="
            log_success "Настройка завершена успешно!"
            echo "=================================================="
            echo
            echo "Теперь вы можете запустить проект:"
            echo "  docker-compose up -d"
            echo
            echo "Или пересобрать образы:"
            echo "  docker-compose up -d --build"
            echo
        else
            log_error "Тестирование не прошло. Проверьте настройки."
            exit 1
        fi
    else
        log_error "Проверка токена не прошла. Попробуйте еще раз."
        exit 1
    fi
}

# Запуск скрипта
main "$@"

#!/bin/bash

# Скрипт развертывания мониторинга RemnaWave
# Автор: RemnaWave Team
# Версия: 1.0

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для вывода
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка прав root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "Этот скрипт должен быть запущен с правами root"
        exit 1
    fi
}

# Проверка существования файлов
check_files() {
    log_info "Проверка необходимых файлов..."
    
    local files=(
        "docker-compose.yaml"
        ".env"
        "nginx/monitoring.conf"
        "static/index.html"
        "static/css/style.css"
        "static/js/app.js"
    )
    
    for file in "${files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Файл $file не найден"
            exit 1
        fi
    done
    
    log_success "Все необходимые файлы найдены"
}

# Проверка зависимостей
check_dependencies() {
    log_info "Проверка зависимостей..."

    # Проверка Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker не установлен. Пожалуйста, сначала установите RemnaWave используя официальный скрипт."
        exit 1
    fi

    # Проверка Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose не установлен. Пожалуйста, сначала установите RemnaWave используя официальный скрипт."
        exit 1
    fi

    # Проверка NGINX
    if ! command -v nginx &> /dev/null; then
        log_error "NGINX не установлен. Пожалуйста, сначала установите RemnaWave используя официальный скрипт."
        exit 1
    fi

    # Проверка установки RemnaWave
    if [[ ! -d "/opt/remnawave" ]]; then
        log_error "RemnaWave не установлен. Пожалуйста, сначала установите RemnaWave используя официальный скрипт."
        exit 1
    fi

    log_success "Все зависимости найдены"
}

# Интеграция с существующим NGINX
integrate_nginx() {
    local domain=${1:-"status.unveilvpn.com"}

    log_info "Интеграция с существующим NGINX RemnaWave..."

    # Проверка существующей конфигурации RemnaWave
    if [[ ! -f "/opt/remnawave/nginx.conf" ]]; then
        log_error "Конфигурация NGINX RemnaWave не найдена в /opt/remnawave/nginx.conf"
        exit 1
    fi

    # Создание резервной копии
    cp /opt/remnawave/nginx.conf /opt/remnawave/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)

    # Добавление upstream для мониторинга
    log_info "Добавление upstream конфигурации мониторинга..."

    if ! grep -q "upstream monitoring_backend" /opt/remnawave/nginx.conf; then
        # Добавляем upstream в начало файла после map блоков
        sed -i '/^ssl_protocols/i\
# RemnaWave Monitoring Upstream\
upstream monitoring_backend {\
    server 127.0.0.1:8080;\
    keepalive 32;\
}\
' /opt/remnawave/nginx.conf
    fi

    # Добавление server блока для мониторинга
    log_info "Добавление server блока для домена мониторинга..."

    if ! grep -q "server_name $domain" /opt/remnawave/nginx.conf; then
        # Создаем временный файл с конфигурацией мониторинга
        cat > /tmp/monitoring_server.conf << EOF

# RemnaWave Monitoring Server Block
server {
    server_name $domain;
    listen 443 ssl;
    http2 on;

    ssl_certificate "/etc/nginx/ssl/$SSL_CERT_DOMAIN/fullchain.pem";
    ssl_certificate_key "/etc/nginx/ssl/$SSL_CERT_DOMAIN/privkey.pem";
    ssl_trusted_certificate "/etc/nginx/ssl/$SSL_CERT_DOMAIN/fullchain.pem";

    # Основная локация для веб-интерфейса мониторинга
    location / {
        proxy_pass http://monitoring_backend;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;

        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # API мониторинга
    location /api/v1/monitoring/ {
        proxy_pass http://monitoring_backend;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
    }

    # WebSocket мониторинга
    location /ws/monitoring {
        proxy_pass http://monitoring_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }

    # Health check
    location /healthcheck {
        proxy_pass http://monitoring_backend;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

        # Добавляем конфигурацию перед последним server блоком (default_server)
        sed -i '/server {$/,/ssl_reject_handshake on;$/i\
' /opt/remnawave/nginx.conf

        # Вставляем конфигурацию мониторинга перед последним server блоком
        sed -i '/server {$/,/ssl_reject_handshake on;$/i\
' /opt/remnawave/nginx.conf

        # Добавляем содержимое файла
        sed -i "/server_name _;/r /tmp/monitoring_server.conf" /opt/remnawave/nginx.conf

        # Удаляем временный файл
        rm -f /tmp/monitoring_server.conf
    fi

    # Проверка конфигурации NGINX
    if nginx -t; then
        log_success "Конфигурация NGINX корректна"
        # Перезагрузка NGINX
        systemctl reload nginx
        log_success "NGINX перезагружен"
    else
        log_error "Ошибка в конфигурации NGINX"
        # Восстановление из резервной копии
        cp /opt/remnawave/nginx.conf.backup.$(date +%Y%m%d_%H%M%S) /opt/remnawave/nginx.conf
        exit 1
    fi

    log_success "NGINX интегрирован с мониторингом"
}

# Проверка SSL сертификатов Cloudflare
check_ssl_certificates() {
    local domain=${1:-"status.unveilvpn.com"}

    log_info "Проверка SSL сертификатов Cloudflare для домена $domain..."

    # Извлечение базового домена
    local base_domain=$(echo "$domain" | sed 's/^[^.]*\.//')

    # Проверка wildcard сертификата
    if [[ -d "/etc/letsencrypt/live/$base_domain" ]]; then
        log_success "Найден wildcard сертификат для $base_domain"
        SSL_CERT_DOMAIN="$base_domain"
        return 0
    fi

    # Проверка сертификата для конкретного домена
    if [[ -d "/etc/letsencrypt/live/$domain" ]]; then
        log_success "Найден сертификат для $domain"
        SSL_CERT_DOMAIN="$domain"
        return 0
    fi

    log_error "SSL сертификат не найден для $domain"
    log_error "Пожалуйста, сначала настройте SSL сертификаты через RemnaWave скрипт"
    log_error "Используйте: remnawave_reverse -> Управление сертификатами домена"
    exit 1
}

# Настройка переменных окружения
setup_environment() {
    log_info "Настройка переменных окружения..."
    
    # Проверка существования .env файла
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.sample" ]]; then
            cp .env.sample .env
            log_info "Создан .env файл из .env.sample"
        else
            log_error ".env файл не найден"
            exit 1
        fi
    fi
    
    # Обновление SERVER_STATUS_URL в .env
    local domain=${1:-"status.unveilvpn.com"}
    sed -i "s|SERVER_STATUS_URL=.*|SERVER_STATUS_URL=\"https://$domain\"|g" .env
    
    log_success "Переменные окружения настроены"
}

# Интеграция мониторинга с существующими контейнерами RemnaWave
deploy_containers() {
    log_info "Интеграция мониторинга с существующими контейнерами RemnaWave..."

    # Переход в директорию RemnaWave
    cd /opt/remnawave

    # Копирование файлов мониторинга
    log_info "Копирование файлов мониторинга..."

    # Создание директории для статических файлов
    mkdir -p /opt/remnawave/static

    # Копирование статических файлов из репозитория
    local script_dir="$(dirname "$0")"
    local project_dir="$(dirname "$script_dir")"

    if [[ -d "$project_dir/static" ]]; then
        cp -r "$project_dir/static"/* /opt/remnawave/static/
        log_success "Статические файлы скопированы"
    else
        log_warning "Статические файлы не найдены в $project_dir/static"
    fi

    # Обновление переменных окружения
    log_info "Обновление переменных окружения..."

    if ! grep -q "SERVER_STATUS_URL" .env; then
        echo "SERVER_STATUS_URL=\"https://$DOMAIN\"" >> .env
    fi

    if ! grep -q "MONITORING_UPDATE_INTERVAL" .env; then
        cat >> .env << EOF

# Monitoring Configuration
MONITORING_UPDATE_INTERVAL=30
MONITORING_TIMEOUT=10
MONITORING_MAX_RETRIES=3
MONITORING_ENABLE_WEBSOCKET=true
MONITORING_ENABLE_NOTIFICATIONS=true
EOF
    fi

    # Обновление docker-compose.yml для включения мониторинга
    log_info "Обновление конфигурации Docker Compose..."

    # Проверяем, не добавлен ли уже порт мониторинга
    if ! grep -q "8080:8080" docker-compose.yml; then
        # Добавляем порт мониторинга к существующему сервису remnawave
        sed -i '/ports:/a\      - "127.0.0.1:8080:8080"  # Monitoring HTTP server' docker-compose.yml
        log_info "Добавлен порт 8080 для мониторинга"
    fi

    # Добавляем volume для статических файлов
    if ! grep -q "/static:" docker-compose.yml; then
        sed -i '/volumes:/a\      - ./static:/static:ro  # Monitoring static files' docker-compose.yml
        log_info "Добавлен volume для статических файлов мониторинга"
    fi

    # Перезапуск контейнеров RemnaWave с новой конфигурацией
    log_info "Перезапуск контейнеров RemnaWave..."

    if docker-compose up -d; then
        log_success "Контейнеры RemnaWave перезапущены с поддержкой мониторинга"
    else
        log_error "Не удалось перезапустить контейнеры"
        exit 1
    fi

    # Ожидание готовности сервисов
    log_info "Ожидание готовности сервисов..."
    sleep 30
}

# Проверка работоспособности
health_check() {
    local domain=${1:-"status.unveilvpn.com"}
    
    log_info "Проверка работоспособности..."
    
    # Проверка локального API
    if curl -s http://localhost:8080/healthcheck > /dev/null; then
        log_success "Локальный API работает"
    else
        log_error "Локальный API недоступен"
        exit 1
    fi
    
    # Проверка API мониторинга
    if curl -s http://localhost:8080/api/v1/monitoring/health > /dev/null; then
        log_success "API мониторинга работает"
    else
        log_error "API мониторинга недоступен"
        exit 1
    fi
    
    # Проверка веб-интерфейса через HTTPS
    if curl -s -k https://$domain > /dev/null; then
        log_success "Веб-интерфейс доступен через HTTPS"
    else
        log_warning "Веб-интерфейс недоступен через HTTPS"
    fi
    
    log_success "Проверка работоспособности завершена"
}

# Проверка автоматического обновления сертификатов
check_cert_renewal() {
    log_info "Проверка настройки автоматического обновления сертификатов..."

    # Проверка существующих cron задач для обновления сертификатов
    if crontab -l 2>/dev/null | grep -q "certbot\|letsencrypt" || \
       find /etc/cron.d/ -name "*certbot*" -o -name "*letsencrypt*" 2>/dev/null | grep -q .; then
        log_success "Автоматическое обновление сертификатов уже настроено"
    else
        log_warning "Автоматическое обновление сертификатов не найдено"
        log_warning "Рекомендуется настроить через RemnaWave скрипт"
    fi
}

# Создание systemd сервиса для автозапуска
setup_systemd() {
    log_info "Настройка systemd сервиса..."
    
    cat > /etc/systemd/system/remnawave-monitoring.service << EOF
[Unit]
Description=RemnaWave Monitoring Service
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/docker-compose up -d
ExecStop=/usr/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF
    
    # Перезагрузка systemd и включение сервиса
    systemctl daemon-reload
    systemctl enable remnawave-monitoring.service
    
    log_success "Systemd сервис настроен"
}

# Вывод информации о развертывании
show_deployment_info() {
    local domain=${1:-"status.unveilvpn.com"}
    
    echo
    log_success "🎉 Развертывание мониторинга RemnaWave завершено!"
    echo
    echo "📊 Веб-интерфейс: https://$domain"
    echo "🔌 API: https://$domain/api/v1/monitoring/"
    echo "📡 WebSocket: wss://$domain/ws/monitoring"
    echo "❤️ Health Check: https://$domain/healthcheck"
    echo
    echo "🔧 Управление сервисом:"
    echo "  Статус:      systemctl status remnawave-monitoring"
    echo "  Перезапуск:  systemctl restart remnawave-monitoring"
    echo "  Логи:        docker-compose logs -f"
    echo
    echo "📝 Конфигурация:"
    echo "  NGINX:       /etc/nginx/sites-available/monitoring"
    echo "  Environment: .env"
    echo "  Логи NGINX:  /var/log/nginx/status.unveilvpn.com.*.log"
    echo
}

# Основная функция
main() {
    local domain=${1:-"status.unveilvpn.com"}
    
    echo "🚀 Развертывание мониторинга RemnaWave"
    echo "📍 Домен: $domain"
    echo
    
    check_root
    check_files
    check_dependencies
    setup_environment "$domain"
    check_ssl_certificates "$domain"
    integrate_nginx "$domain"
    deploy_containers
    check_cert_renewal
    setup_systemd
    health_check "$domain"
    show_deployment_info "$domain"
}

# Обработка аргументов командной строки
case "${1:-}" in
    --help|-h)
        echo "Использование: $0 [домен]"
        echo "Пример: $0 status.unveilvpn.com"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac

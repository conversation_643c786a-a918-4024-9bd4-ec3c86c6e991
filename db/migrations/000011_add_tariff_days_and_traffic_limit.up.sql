-- Миграция: добавление полей для количества дней и лимита трафика в тарифы
ALTER TABLE tariffs ADD COLUMN days INTEGER NOT NULL DEFAULT 30;
ALTER TABLE tariffs ADD COLUMN traffic_limit_gb INTEGER NOT NULL DEFAULT 0; -- 0 означает безлимитный трафик

-- Обновляем существующие тарифы на основе их кодов
-- 1m = 30 дней, 3m = 90 дней, 6m = 180 дней, 12m = 365 дней
UPDATE tariffs SET days = 30 WHERE code = '1m';
UPDATE tariffs SET days = 90 WHERE code = '3m';
UPDATE tariffs SET days = 180 WHERE code = '6m';
UPDATE tariffs SET days = 365 WHERE code = '12m';

-- Все существующие тарифы делаем безлимитными (traffic_limit_gb = 0)
UPDATE tariffs SET traffic_limit_gb = 0;

-- Добавляем комментарии к новым полям
COMMENT ON COLUMN tariffs.days IS 'Количество дней действия тарифа';
COMMENT ON COLUMN tariffs.traffic_limit_gb IS 'Лимит трафика в ГБ (0 = безлимитный)';

-- Миграция: добавление полей для отслеживания отправленных уведомлений о подписке
-- last_notification_sent_at - время последнего отправленного уведомления
-- notification_days_sent - JSON массив дней, за которые уже отправлены уведомления

ALTER TABLE customer ADD COLUMN last_notification_sent_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE customer ADD COLUMN notification_days_sent JSONB DEFAULT '[]'::jsonb;

-- Создаем индекс для быстрого поиска по времени последнего уведомления
CREATE INDEX idx_customer_last_notification ON customer(last_notification_sent_at);

-- Ком<PERSON>ентарии для полей
COMMENT ON COLUMN customer.last_notification_sent_at IS 'Время последнего отправленного уведомления о подписке';
COMMENT ON COLUMN customer.notification_days_sent IS 'JSON массив дней (1, 2, 3), за которые уже отправлены уведомления об истечении подписки';

# Docker Compose конфигурация для развертывания с мониторингом
# Использование: docker-compose -f docker-compose.yaml -f docker-compose.monitoring.yaml up -d

version: '3.8'

services:
  bot:
    # Расширяем основной сервис бота
    ports:
      - "127.0.0.1:8080:8080"  # Открываем порт для HTTP сервера мониторинга
    volumes:
      - ./static:/static:ro  # Монтируем статические файлы для веб-интерфейса
    environment:
      # Переменные для мониторинга
      - SERVER_STATUS_URL=https://status.unveilvpn.com
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx reverse proxy для мониторинга
  nginx:
    image: nginx:alpine
    container_name: remnawave-monitoring-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/monitoring.conf:/etc/nginx/conf.d/default.conf:ro
      - /etc/ssl/certs:/etc/ssl/certs:ro
      - /etc/ssl/private:/etc/ssl/private:ro
      - /var/log/nginx:/var/log/nginx
    depends_on:
      - bot
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  # Prometheus для метрик (опционально)
  prometheus:
    image: prom/prometheus:latest
    container_name: remnawave-monitoring-prometheus
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring-full

  # Grafana для визуализации метрик (опционально)
  grafana:
    image: grafana/grafana:latest
    container_name: remnawave-monitoring-grafana
    ports:
      - "127.0.0.1:3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring-full

  # Node Exporter для системных метрик (опционально)
  node-exporter:
    image: prom/node-exporter:latest
    container_name: remnawave-monitoring-node-exporter
    ports:
      - "127.0.0.1:9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped
    profiles:
      - monitoring-full

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  default:
    name: remnawave-monitoring
    driver: bridge

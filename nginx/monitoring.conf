# Конфигурация мониторинга RemnaWave для интеграции с существующим NGINX
# Этот блок должен быть добавлен в существующий файл /opt/remnawave/nginx.conf

# Upstream для приложения мониторинга (добавить в начало файла)
upstream monitoring_backend {
    server 127.0.0.1:8080;
    keepalive 32;
}

# Дополнительный server блок для мониторинга (добавить после существующих server блоков)
server {
    listen 443 ssl http2;
    server_name status.unveilvpn.com;

    # SSL сертификаты (используем существующие сертификаты Cloudflare)
    # Путь будет определен автоматически: либо wildcard, либо конкретный домен
    ssl_certificate "/etc/nginx/ssl/CERT_DOMAIN/fullchain.pem";
    ssl_certificate_key "/etc/nginx/ssl/CERT_DOMAIN/privkey.pem";
    ssl_trusted_certificate "/etc/nginx/ssl/CERT_DOMAIN/fullchain.pem";

    # SSL настройки (используем те же, что и в RemnaWave)
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ecdh_curve X25519:prime256v1:secp384r1;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers on;
    ssl_session_timeout 1d;
    ssl_session_cache shared:MozSSL:10m;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' ws: wss:;" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Основная локация для веб-интерфейса
    location / {
        proxy_pass http://monitoring_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Таймауты
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Буферизация
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # API эндпоинты мониторинга
    location /api/v1/monitoring/ {
        proxy_pass http://monitoring_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers для API
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
        
        # Обработка preflight запросов
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
        
        # Кэширование для API
        location ~* \.(json)$ {
            expires 30s;
            add_header Cache-Control "public, no-transform";
        }
    }
    
    # WebSocket для real-time обновлений
    location /ws/monitoring {
        proxy_pass http://monitoring_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket таймауты
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }
    
    # Статические файлы с кэшированием
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://monitoring_backend;
        proxy_set_header Host $host;
        
        # Кэширование статических файлов
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Сжатие
        gzip_static on;
    }
    
    # Health check эндпоинт
    location /healthcheck {
        proxy_pass http://monitoring_backend;
        proxy_set_header Host $host;
        access_log off;
    }
    
    # Логирование
    access_log /var/log/nginx/status.unveilvpn.com.access.log;
    error_log /var/log/nginx/status.unveilvpn.com.error.log;
}

# Пример интеграции мониторинга в существующий server блок панели
# Добавьте эти location блоки в существующий server блок для panel.unveilvpn.com

# Мониторинг как поддиректория панели
location /status {
    proxy_pass http://monitoring_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;

    # Таймауты
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
}

# API мониторинга через панель
location /api/v1/monitoring/ {
    proxy_pass http://monitoring_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # CORS headers для API
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;
}

# WebSocket мониторинга через панель
location /ws/monitoring {
    proxy_pass http://monitoring_backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # WebSocket таймауты
    proxy_connect_timeout 7d;
    proxy_send_timeout 7d;
    proxy_read_timeout 7d;
}

# Rate limiting для защиты от DDoS
limit_req_zone $binary_remote_addr zone=monitoring_api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=monitoring_web:10m rate=5r/s;

# Применение rate limiting в server блоках:
# location /api/v1/monitoring/ {
#     limit_req zone=monitoring_api burst=20 nodelay;
#     ...
# }
#
# location / {
#     limit_req zone=monitoring_web burst=10 nodelay;
#     ...
# }

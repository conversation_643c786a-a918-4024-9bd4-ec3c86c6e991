package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

// Структуры данных для API
type SystemStatus struct {
	TotalNodes       int    `json:"total_nodes"`
	OnlineNodes      int    `json:"online_nodes"`
	OfflineNodes     int    `json:"offline_nodes"`
	ErrorNodes       int    `json:"error_nodes"`
	LastUpdate       string `json:"last_update"`
	SystemHealth     string `json:"system_health"`
	AverageLatencyMs int    `json:"average_latency_ms"`
}

type NodeLoad struct {
	CPUPercent    float64 `json:"cpu_percent"`
	MemoryPercent float64 `json:"memory_percent"`
	ActiveUsers   int     `json:"active_users"`
}

type NodeStatus struct {
	ID               string   `json:"id"`
	Name             string   `json:"name"`
	Address          string   `json:"address"`
	Status           string   `json:"status"`
	LastSeen         string   `json:"last_seen"`
	LatencyMs        int      `json:"latency_ms"`
	Location         string   `json:"location"`
	Version          string   `json:"version"`
	UptimePercentage float64  `json:"uptime_percentage"`
	Load             NodeLoad `json:"load"`
}

type ApiResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data"`
	Meta    struct {
		Timestamp string `json:"timestamp"`
		Version   string `json:"version"`
	} `json:"meta"`
}

func main() {
	mux := http.NewServeMux()

	// API эндпоинты для мониторинга
	mux.HandleFunc("/api/v1/monitoring/system", handleSystemStatus)
	mux.HandleFunc("/api/v1/monitoring/nodes/status", handleNodesStatus)

	// Обслуживание статических файлов для веб-интерфейса мониторинга
	staticHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Убираем префикс /status/ из пути
		path := strings.TrimPrefix(r.URL.Path, "/status/")

		// Установка правильных MIME типов
		switch {
		case strings.HasSuffix(path, ".css"):
			w.Header().Set("Content-Type", "text/css; charset=utf-8")
		case strings.HasSuffix(path, ".js"):
			w.Header().Set("Content-Type", "application/javascript; charset=utf-8")
		case strings.HasSuffix(path, ".html"):
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
		case strings.HasSuffix(path, ".json"):
			w.Header().Set("Content-Type", "application/json; charset=utf-8")
		case strings.HasSuffix(path, ".png"):
			w.Header().Set("Content-Type", "image/png")
		case strings.HasSuffix(path, ".jpg"), strings.HasSuffix(path, ".jpeg"):
			w.Header().Set("Content-Type", "image/jpeg")
		case strings.HasSuffix(path, ".svg"):
			w.Header().Set("Content-Type", "image/svg+xml")
		case strings.HasSuffix(path, ".ico"):
			w.Header().Set("Content-Type", "image/x-icon")
		}

		// Создаем новый запрос с исправленным путем
		newReq := r.Clone(r.Context())
		newReq.URL.Path = "/" + path

		// Обслуживание файла
		http.FileServer(http.Dir("./static/")).ServeHTTP(w, newReq)
	})

	mux.Handle("/status/", staticHandler)
	mux.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		http.ServeFile(w, r, "./static/index.html")
	})

	// Перенаправление с корневого пути на /status
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			http.Redirect(w, r, "/status", http.StatusMovedPermanently)
		} else {
			http.NotFound(w, r)
		}
	})

	log.Println("Тестовый сервер запущен на порту 8080")
	log.Println("Откройте http://localhost:8080/status для тестирования")
	log.Fatal(http.ListenAndServe(":8080", mux))
}

func handleSystemStatus(w http.ResponseWriter, r *http.Request) {
	// CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	totalNodes := 12
	onlineNodes := rand.Intn(5) + 8  // 8-12
	offlineNodes := totalNodes - onlineNodes
	errorNodes := rand.Intn(3)       // 0-2

	systemStatus := SystemStatus{
		TotalNodes:       totalNodes,
		OnlineNodes:      onlineNodes,
		OfflineNodes:     offlineNodes,
		ErrorNodes:       errorNodes,
		LastUpdate:       time.Now().Format(time.RFC3339),
		SystemHealth:     getSystemHealth(onlineNodes),
		AverageLatencyMs: rand.Intn(30) + 15, // 15-45ms
	}

	response := ApiResponse{
		Success: true,
		Data:    systemStatus,
	}
	response.Meta.Timestamp = time.Now().Format(time.RFC3339)
	response.Meta.Version = "1.0.0"

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleNodesStatus(w http.ResponseWriter, r *http.Request) {
	// CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	locations := []string{
		"Netherlands, Amsterdam", "Germany, Frankfurt", "United States, New York",
		"United Kingdom, London", "France, Paris", "Singapore", "Japan, Tokyo",
		"Canada, Toronto", "Australia, Sydney", "Sweden, Stockholm",
		"Switzerland, Zurich", "Poland, Warsaw",
	}

	var nodes []NodeStatus
	for i := 0; i < 12; i++ {
		status := getRandomStatus()
		
		node := NodeStatus{
			ID:               fmt.Sprintf("node-%02d", i+1),
			Name:             fmt.Sprintf("VPN-%02d", i+1),
			Address:          fmt.Sprintf("xxx.xxx.xxx.%d", i+1), // IP уже скрыт
			Status:           status,
			LastSeen:         time.Now().Format(time.RFC3339),
			LatencyMs:        getLatency(status),
			Location:         locations[i],
			Version:          "v2.1.3",
			UptimePercentage: getUptime(status),
			Load: NodeLoad{
				CPUPercent:    getRandomFloat(5.0, 85.0, status),
				MemoryPercent: getRandomFloat(20.0, 75.0, status),
				ActiveUsers:   getActiveUsers(status),
			},
		}
		nodes = append(nodes, node)
	}

	response := ApiResponse{
		Success: true,
		Data:    nodes,
	}
	response.Meta.Timestamp = time.Now().Format(time.RFC3339)
	response.Meta.Version = "1.0.0"

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func getSystemHealth(onlineNodes int) string {
	if onlineNodes >= 8 {
		return "healthy"
	} else if onlineNodes >= 4 {
		return "degraded"
	}
	return "down"
}

func getRandomStatus() string {
	statuses := []string{"online", "online", "online", "online", "offline", "error"}
	return statuses[rand.Intn(len(statuses))]
}

func getLatency(status string) int {
	if status == "online" {
		return rand.Intn(70) + 10 // 10-80ms
	}
	return 0
}

func getUptime(status string) float64 {
	if status == "online" {
		return 95.0 + rand.Float64()*4.9 // 95.0-99.9%
	}
	return 0.0
}

func getRandomFloat(min, max float64, status string) float64 {
	if status != "online" {
		return 0.0
	}
	return min + rand.Float64()*(max-min)
}

func getActiveUsers(status string) int {
	if status == "online" {
		return rand.Intn(150)
	}
	return 0
}
